package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CompareModelMerticTypeEnum {

    COMPARE_METRIC(1, "比对指标"),

    BASELINE_METRIC(2, "基准值白哦");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompareModelMerticTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompareModelMerticTypeEnum> map = new HashMap<>();

    static {
        for (CompareModelMerticTypeEnum item : CompareModelMerticTypeEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static CompareModelMerticTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }

}
