package com.yyigou.ddc.dmp.model.bo.sqlparser;

import lombok.Data;

import java.io.Serializable;

@Data
public class ColumnMetaBO implements Serializable {
    private String schemaName;
    private String tableName;
    private String columnName;
    private String columnType;
    private Long length;
    private Integer numPrecision;
    private Integer numScale;
    private boolean nullable;
    private String defaultValue;
    private String comment;
    private int columnOrder;
    private String columnKey;
}

