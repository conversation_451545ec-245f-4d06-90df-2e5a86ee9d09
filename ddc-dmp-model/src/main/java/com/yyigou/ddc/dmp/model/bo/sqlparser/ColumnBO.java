package com.yyigou.ddc.dmp.model.bo.sqlparser;

import lombok.Data;

import java.io.Serializable;

@Data
public class ColumnBO implements Serializable {
    private String schemaName;
    private String tableName;
    private String columnName;
    // 如果有重复字符，这里会在字段名后加加上数字
    private String parsedColumnName;
    private boolean renamed;
    private String columnType;
    private Long length;
    private Integer numPrecision;
    private Integer numScale;
    private boolean nullable;
    private String defaultValue;
    private String comment;
    private int columnOrder;
    private String columnKey;

    private TableBO tableBO;
}

