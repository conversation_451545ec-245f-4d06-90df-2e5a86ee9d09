package com.yyigou.ddc.dmp.model.res.dataset;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ModelFieldRes {
    /**
     * 主键ID
     */
//    private Long id;

    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 所属数据模型ID
     */
    private String modelNo;

    /**
     * 字段编码
     */
    private String fieldCode;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段数据类型
     */
    private String dataType;

    /**
     * 日期格式
     */
    private String dateFormat;

    /**
     * 字段长度
     */
    private Long length;

    /**
     * 数值精度
     */
    private Integer numPrecision;

    /**
     * 数值小数位
     */
    private Integer numScale;

    /**
     * 是否可空
     */
    private boolean nullable;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 字段排序
     */
    private int fieldOrder;

    /**
     * 字段键
     */
    private String fieldKey;

    /**
     * 字段描述
     */
    private String description;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    private Integer status;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人编号
     */
    private String modifyNo;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 最后操作时间
     */
    private LocalDateTime opTimestamp;

    private ModelRes modelRes;
}