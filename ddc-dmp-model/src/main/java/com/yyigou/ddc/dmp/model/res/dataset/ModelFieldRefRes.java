package com.yyigou.ddc.dmp.model.res.dataset;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据集模型关系关联字段
 */
@Data
public class ModelFieldRefRes {
    /**
     * 主键ID
     */
//    private Long id;

    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 数据模型关系唯一标识
     */
    private String dataModelRefNo;

    /**
     * 源数据模型字段
     */
    private String sourceFieldCode;

    /**
     * 目标数据模型字段
     */
    private String targetFieldCode;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人编号
     */
    private String modifyNo;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 最后操作时间
     */
    private LocalDateTime opTimestamp;
}