# 数据集API接口文档

本文档描述了数据集模块新增的4个核心接口的使用方法和实现细节。

## 接口概览

| 接口名称 | 功能描述 | HTTP方法 | 路径 |
|---------|---------|---------|------|
| saveDataset | 保存数据集 | POST | /dataset/save |
| getDataset | 获取数据集详情 | GET | /dataset/get |
| previewData | 预览数据集数据 | POST | /dataset/preview |
| validateDataset | 校验数据集配置 | POST | /dataset/validate |

## 1. saveDataset - 保存数据集

### 功能描述
创建或更新数据集配置信息。

### 请求参数
```json
{
  "datasetNo": "数据集唯一标识（可选，新增时不传）",
  "datasetCode": "数据集编码",
  "datasetName": "数据集名称",
  "drivingSchemaName": "驱动表schema名称",
  "drivingTableName": "驱动表名称",
  "description": "数据集描述",
  "status": 1
}
```

### 响应结果
```json
{
  "datasetNo": "生成的数据集唯一标识"
}
```

## 2. getDataset - 获取数据集详情

### 功能描述
根据数据集编号查询数据集详细信息，包含关联的模型列表。

### 请求参数
```json
{
  "datasetNo": "数据集唯一标识"
}
```

### 响应结果
```json
{
  "datasetNo": "数据集唯一标识",
  "datasetName": "数据集名称",
  "description": "数据集描述",
  "status": 1,
  "modelNoList": ["模型编号1", "模型编号2"],
  "createTime": "创建时间",
  "modifyTime": "修改时间"
}
```

## 3. previewData - 预览数据集数据

### 功能描述
根据数据集配置生成SQL查询并返回预览数据，默认返回10条记录。

### 请求参数
```json
{
  "datasetNo": "数据集唯一标识",
  "limit": 10
}
```

### 响应结果
```json
{
  "datasetNo": "数据集唯一标识",
  "data": [
    {
      "field1": "value1",
      "field2": "value2"
    }
  ],
  "columns": [
    {
      "fieldName": "字段名称",
      "displayName": "字段显示名称",
      "dataType": "字段数据类型",
      "sourceTable": "字段来源表",
      "sourceSchema": "字段来源schema"
    }
  ],
  "totalCount": 10
}
```

## 4. validateDataset - 校验数据集配置

### 功能描述
校验数据集的表结构和字段配置是否合法，使用Calcite解析SQL并验证字段存在性。

### 请求参数
```json
{
  "datasetNo": "数据集唯一标识",
  "drivingSchemaName": "驱动表schema名称",
  "drivingTableName": "驱动表名称",
  "joinTables": [
    {
      "schemaName": "关联表schema名称",
      "tableName": "关联表名称",
      "joinType": "innerJoin|leftJoin|rightJoin",
      "joinConditions": [
        {
          "leftField": "左表字段",
          "rightField": "右表字段",
          "operator": "=|!=|>|>=|<|<="
        }
      ]
    }
  ]
}
```

### 响应结果
```json
{
  "datasetNo": "数据集唯一标识",
  "valid": true,
  "errors": [
    {
      "errorType": "FIELD_NOT_FOUND|TABLE_NOT_FOUND|INVALID_SQL",
      "message": "错误消息",
      "tableName": "相关表名",
      "fieldName": "相关字段名"
    }
  ],
  "validFields": [
    {
      "fieldName": "字段名称",
      "dataType": "字段数据类型",
      "sourceTable": "字段来源表",
      "sourceSchema": "字段来源schema"
    }
  ]
}
```

## 技术实现细节

### 数据预览实现
1. 查询数据集基本信息和关联模型
2. 根据驱动表或模型SQL构建预览查询
3. 执行查询并返回限定条数的数据
4. 构建字段元信息

### 数据校验实现
1. 构建校验SQL（包含JOIN条件）
2. 使用Calcite解析SQL获取字段信息
3. 加载相关schema的元数据
4. 校验表和字段是否存在
5. 返回校验结果和错误信息

### 依赖组件
- **DorisDataAccessService**: 数据查询服务
- **SqlExtractor**: SQL解析工具（基于Calcite）
- **SchemaLoader**: 元数据加载器
- **DataModelService**: 数据模型服务

## 使用示例

### 创建数据集
```bash
curl -X POST http://localhost:8080/dataset/save \
  -H "Content-Type: application/json" \
  -d '{
    "datasetName": "采购订单数据集",
    "drivingSchemaName": "dwd",
    "drivingTableName": "purchase_order",
    "description": "采购订单相关数据集"
  }'
```

### 预览数据
```bash
curl -X POST http://localhost:8080/dataset/preview \
  -H "Content-Type: application/json" \
  -d '{
    "datasetNo": "dataset-001",
    "limit": 5
  }'
```

### 校验配置
```bash
curl -X POST http://localhost:8080/dataset/validate \
  -H "Content-Type: application/json" \
  -d '{
    "datasetNo": "dataset-001",
    "drivingSchemaName": "dwd",
    "drivingTableName": "purchase_order",
    "joinTables": [{
      "schemaName": "dwd",
      "tableName": "supplier",
      "joinType": "leftJoin",
      "joinConditions": [{
        "leftField": "t0.supplier_id",
        "rightField": "t1.id",
        "operator": "="
      }]
    }]
  }'
```

## 注意事项

1. 所有接口都需要传入租户编号（enterpriseNo），当前硬编码为"2000002"
2. 数据预览功能依赖于正确的数据集和模型配置
3. 数据校验功能需要Calcite预热完成后才能正常使用
4. 建议在生产环境中对预览数据的条数进行限制，避免查询大量数据影响性能
