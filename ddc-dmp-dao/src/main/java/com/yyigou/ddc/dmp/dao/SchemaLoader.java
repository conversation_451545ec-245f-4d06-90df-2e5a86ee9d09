package com.yyigou.ddc.dmp.dao;

import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnMetaBO;
import com.yyigou.ddc.dmp.model.bo.sqlparser.TableMetaBO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

@Component
public class SchemaLoader {
    @Value("${spring.datasource.meta.mysql.host}")
    private String mysqlHost;
    @Value("${spring.datasource.meta.mysql.port}")
    private int mysqlPort;
    @Value("${spring.datasource.meta.mysql.username}")
    private String mysqlUsername;
    @Value("${spring.datasource.meta.mysql.password}")
    private String mysqlPassword;

    @Value("${spring.datasource.meta.doris.host}")
    private String dorisHost;
    @Value("${spring.datasource.meta.doris.port}")
    private int dorisPort;
    @Value("${spring.datasource.meta.doris.username}")
    private String dorisUsername;
    @Value("${spring.datasource.meta.doris.password}")
    private String dorisPassword;

    public Map<String, TableMetaBO> loadTableMeta(String... schemas) throws SQLException {
        Map<String, TableMetaBO> tableMetaBOMap = new HashMap<>();

        if (schemas == null || schemas.length == 0) {
            return tableMetaBOMap;
        }

        String inClause = String.join(",",
                java.util.Arrays.stream(schemas).map(s -> "'" + s + "'").toArray(String[]::new));
        String sql = "SELECT table_schema, table_name, table_comment " +
                "FROM information_schema.tables " +
                "WHERE table_schema IN (" + inClause + ")";

        {
            try (Connection conn = DriverManager.getConnection(String.format("jdbc:mysql://%s:%s", mysqlHost, mysqlPort), mysqlUsername, mysqlPassword);
                 PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String schema = rs.getString("table_schema");
                    String table = rs.getString("table_name");
                    String comment = rs.getString("table_comment");
                    String qualifiedTable = schema + "." + table;

                    TableMetaBO tableMetaBO = new TableMetaBO();
                    tableMetaBO.setSchemaName(schema);
                    tableMetaBO.setTableName(table);
                    tableMetaBO.setComment(comment);

                    tableMetaBOMap.put(qualifiedTable, tableMetaBO);
                }
            }
        }

        {
            try (Connection conn = DriverManager.getConnection(String.format("jdbc:mysql://%s:%s", dorisHost, dorisPort), dorisUsername, dorisPassword);
                 PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String schema = rs.getString("table_schema");
                    String table = rs.getString("table_name");
                    String comment = rs.getString("table_comment");
                    String qualifiedTable = schema + "." + table;

                    TableMetaBO tableMetaBO = new TableMetaBO();
                    tableMetaBO.setSchemaName(schema);
                    tableMetaBO.setTableName(table);
                    tableMetaBO.setComment(comment);

                    tableMetaBOMap.put(qualifiedTable, tableMetaBO);
                }
            }
        }

        return tableMetaBOMap;
    }


    public Map<String, ColumnMetaBO> loadColumnMeta(String... schemas) throws SQLException {
        Map<String, ColumnMetaBO> columnMetaBOMap = new HashMap<>();

        if (schemas == null || schemas.length == 0) {
            return columnMetaBOMap;
        }

        String inClause = String.join(",",
                java.util.Arrays.stream(schemas).map(s -> "'" + s + "'").toArray(String[]::new));

        String sql = "SELECT table_schema, table_name, column_name, is_nullable, " +
                "character_maximum_length, numeric_precision, numeric_scale, " +
                "column_type, column_comment, ordinal_position, column_default, column_key " +
                "FROM information_schema.columns WHERE table_schema IN (" + inClause + ")";

        {
            try (Connection conn = DriverManager.getConnection(String.format("jdbc:mysql://%s:%s", mysqlHost, mysqlPort), mysqlUsername, mysqlPassword);
                 Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    String schema = rs.getString("table_schema").toLowerCase();
                    String table = rs.getString("table_name").toLowerCase();
                    String column = rs.getString("column_name").toLowerCase();
                    String type = rs.getString("column_type");
                    String nullable = rs.getString("is_nullable");
                    Long length = rs.getLong("character_maximum_length");
                    Integer precision = rs.getInt("numeric_precision");
                    Integer scale = rs.getInt("numeric_scale");
                    String defaultValue = rs.getString("column_default");
                    String comment = rs.getString("column_comment");
                    int idx = rs.getInt("ordinal_position");
                    String columnKey = rs.getString("column_key");

                    String key = schema + "." + table + "." + column;

                    ColumnMetaBO meta = new ColumnMetaBO();
                    meta.setSchemaName(schema);
                    meta.setTableName(table);
                    meta.setColumnName(column);
                    meta.setColumnType(type);
                    meta.setLength(length);
                    meta.setNumPrecision(precision);
                    meta.setNumScale(scale);
                    meta.setNullable("YES".equals(nullable));
                    meta.setDefaultValue(defaultValue);
                    meta.setComment(comment);
                    meta.setColumnOrder(idx);
                    meta.setColumnKey(columnKey);

                    columnMetaBOMap.put(key, meta);
                }
            }
        }

        {
            try (Connection conn = DriverManager.getConnection(String.format("jdbc:mysql://%s:%s", dorisHost, dorisPort), dorisUsername, dorisPassword);
                 Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    String schema = rs.getString("table_schema").toLowerCase();
                    String table = rs.getString("table_name").toLowerCase();
                    String column = rs.getString("column_name").toLowerCase();
                    String type = rs.getString("column_type");
                    String nullable = rs.getString("is_nullable");
                    Long length = rs.getLong("character_maximum_length");
                    Integer precision = rs.getInt("numeric_precision");
                    Integer scale = rs.getInt("numeric_scale");
                    String defaultValue = rs.getString("column_default");
                    String comment = rs.getString("column_comment");
                    int idx = rs.getInt("ordinal_position");

                    String key = schema + "." + table + "." + column;

                    ColumnMetaBO meta = new ColumnMetaBO();
                    meta.setSchemaName(schema);
                    meta.setTableName(table);
                    meta.setColumnName(column);
                    meta.setColumnType(type);
                    meta.setLength(length);
                    meta.setNumPrecision(precision);
                    meta.setNumScale(scale);
                    meta.setNullable("YES".equals(nullable));
                    meta.setDefaultValue(defaultValue);
                    meta.setComment(comment);
                    meta.setColumnOrder(idx);

                    columnMetaBOMap.put(key, meta);
                }
            }
        }

        return columnMetaBOMap;
    }
}
