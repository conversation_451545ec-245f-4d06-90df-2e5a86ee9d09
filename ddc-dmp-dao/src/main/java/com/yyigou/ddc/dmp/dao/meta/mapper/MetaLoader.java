package com.yyigou.ddc.dmp.dao.meta.mapper;

import com.yyigou.ddc.dmp.dao.meta.entity.Columns;
import com.yyigou.ddc.dmp.dao.meta.entity.Schemata;
import com.yyigou.ddc.dmp.dao.meta.entity.Tables;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Types;
import java.util.List;

@Component
public class MetaLoader {
    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;

    private final static String SCHEMATA_SQL = "SELECT * FROM information_schema.schemata";
    private final static String TABLES_SQL = "SELECT * FROM information_schema.tables WHERE table_schema = ?";
    private final static String COLUMNS_SQL = "SELECT * FROM information_schema.columns WHERE table_schema = ? AND table_name = ?";

    public List<Schemata> loadSchemata() {
        return dorisJdbcTemplate.query(SCHEMATA_SQL, new BeanPropertyRowMapper<>(Schemata.class));
    }

    public List<Tables> loadTables(String schemaName) {
        return dorisJdbcTemplate.query(TABLES_SQL, new Object[]{schemaName}, new int[]{Types.VARCHAR}, new BeanPropertyRowMapper<>(Tables.class));
    }

    public List<Columns> loadColumns(String schemaName, String tableName) {
        return dorisJdbcTemplate.query(COLUMNS_SQL, new Object[]{schemaName, tableName}, new int[]{Types.VARCHAR, Types.VARCHAR}, new BeanPropertyRowMapper<>(Columns.class));
    }
}
