package com.yyigou.ddc.dmp.dao.dataset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.sql.Timestamp;

@Data
@TableName("dmp_dataset_join_rel")
public class DatasetJoinRel {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String enterpriseNo;
    private String datasetNo;
    private String sourceSchemaName;
    private String sourceTableName;
    private String targetSchemaName;
    private String targetTableName;
    private String joinType;
    private String joinCondition;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private Timestamp opTimestamp;
}

