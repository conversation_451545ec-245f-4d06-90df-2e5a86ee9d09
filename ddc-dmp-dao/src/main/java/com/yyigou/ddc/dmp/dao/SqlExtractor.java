package com.yyigou.ddc.dmp.dao;

import com.mysql.cj.jdbc.MysqlDataSource;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnBO;
import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnMetaBO;
import com.yyigou.ddc.dmp.model.bo.sqlparser.TableBO;
import com.yyigou.ddc.dmp.model.bo.sqlparser.TableMetaBO;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.adapter.jdbc.JdbcSchema;
import org.apache.calcite.config.Lex;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.plan.RelOptTable;
import org.apache.calcite.rel.RelNode;
import org.apache.calcite.rel.core.Project;
import org.apache.calcite.rel.core.TableScan;
import org.apache.calcite.rel.metadata.RelColumnOrigin;
import org.apache.calcite.rel.metadata.RelMetadataQuery;
import org.apache.calcite.rel.type.RelDataTypeField;
import org.apache.calcite.rex.RexInputRef;
import org.apache.calcite.rex.RexNode;
import org.apache.calcite.schema.SchemaPlus;
import org.apache.calcite.schema.impl.AbstractSchema;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlOperatorTable;
import org.apache.calcite.sql.fun.SqlLibrary;
import org.apache.calcite.sql.fun.SqlLibraryOperatorTableFactory;
import org.apache.calcite.sql.fun.SqlStdOperatorTable;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.util.SqlOperatorTables;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.apache.calcite.tools.FrameworkConfig;
import org.apache.calcite.tools.Frameworks;
import org.apache.calcite.tools.Planner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j
public class SqlExtractor {
    @Value("${spring.datasource.meta.database-yyigou-dsrp}")
    private String  database_yyigou_dsrp = "yyigou_dsrp";

    @Value("${spring.datasource.meta.database-yyigou-ddc}")
    private String database_yyigou_ddc = "yyigou_ddc";

    @Value("${spring.datasource.meta.database-yyigou-dwd}")
    private String database_yyigou_dwd = "test_dwd";

    @Value("${spring.datasource.meta.schema-catalog}")
    private String schema_catalog = "mysql_rds";

    @Value("${spring.datasource.meta.schema-yyigou-dsrp}")
    private String schema_yyigou_dsrp = "yyigou_dsrp";

    @Value("${spring.datasource.meta.schema-yyigou-ddc}")
    private String schema_yyigou_ddc = "yyigou_ddc";

    @Value("${spring.datasource.meta.schema-yyigou-dwd}")
    private String schema_yyigou_dwd = "dwd";

    @Value("${spring.datasource.meta.mysql.host}")
    private String mysqlHost;

    @Value("${spring.datasource.meta.mysql.port}")
    private int mysqlPort;

    @Value("${spring.datasource.meta.mysql.username}")
    private String mysqlUsername;

    @Value("${spring.datasource.meta.mysql.password}")
    private String mysqlPassword;

    @Value("${spring.datasource.meta.doris.host}")
    private String dorisHost;

    @Value("${spring.datasource.meta.doris.port}")
    private int dorisPort;

    @Value("${spring.datasource.meta.doris.username}")
    private String dorisUsername;

    @Value("${spring.datasource.meta.doris.password}")
    private String dorisPassword;

    @Value("classpath:warmup.sql")
    private org.springframework.core.io.Resource resource;

    @Resource
    private SchemaLoader schemaLoader;

    private final AtomicBoolean isWarmedUp = new AtomicBoolean(false);

    private SchemaPlus catalogSchema;

    @PostConstruct
    private void init() throws Exception {
        catalogSchema = buildSchema();

        new Thread(
                ()->{
                    try {
                        log.warn("warm up...");
                        long l = System.currentTimeMillis();

                        InputStream inputStream = resource.getInputStream();
                        String sql = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                        inputStream.close();

                        parseSQL(sql, true);

                        isWarmedUp.set(true);

                        log.warn("warm up cost: {}ms", System.currentTimeMillis() - l);
                    } catch (Exception e) {
                        log.error("warm up error", e);
                    }
            }
        ).start();
    }

    private Map<String, Object> sqlTrans(String rawSql) {
        // 删除空格
        String sql = rawSql.trim();

        // 删除末尾的分号
        String lastChar = sql.substring(sql.length() - 1);
        if (";".equals(lastChar)) {
            sql = sql.substring(0, sql.length() - 1);
        }

        // 因为if表达式作为where条件，不影响字段解析，所以直接删除
        // 删除 ${if(...)} 表达式
//        sql = sql.replaceAll("\\$\\{if\\([^}]*\\)\\}", "");

        // 因为简单变量不影响字段解析，所以直接删除
        // 删除简单变量 ${xxx}
        sql = sql.replaceAll("\\$\\{[^}]+\\}", "");

        // 替换 IFNULL 为 COALESCE，否则无法识别IFNULL函数
        sql = sql.replace("IFNULL", "COALESCE");

        // 处理 no 字段，作为关键字不能裸用，NO ACTION（外键约束）、NO INDEX（某些数据库的 DDL）、NO WAIT（锁超时选项）
        sql = sql.replaceAll("\\.no", ".`no`");
        sql = sql.replaceAll("\\sno", " `no`");

        // 处理中文，由于解析时不使用utf8mb4，所以替换中文为Unicode
        // 存储 unicode -> 中文
        Map<String, String> unicodeToChineseMap = new HashMap<>();
        sql = replaceChinese(unicodeToChineseMap, sql);

        Map<String, Object> result = new HashMap<>();
        result.put("sql", sql);
        result.put("unicodeToChineseMap", unicodeToChineseMap);

        return result;
    }

    private Map<String, MysqlDataSource> getDatasource() {
        MysqlDataSource dataSource_yyigou_dsrp = new MysqlDataSource();
        dataSource_yyigou_dsrp.setUrl(String.format("jdbc:mysql://%s:%s/%s", mysqlHost, mysqlPort, database_yyigou_dsrp));
        dataSource_yyigou_dsrp.setServerName(mysqlHost);
        dataSource_yyigou_dsrp.setPortNumber(mysqlPort);
        dataSource_yyigou_dsrp.setDatabaseName(database_yyigou_dsrp);
        dataSource_yyigou_dsrp.setUser(mysqlUsername);
        dataSource_yyigou_dsrp.setPassword(mysqlPassword);

        MysqlDataSource dataSource_yyigou_ddc = new MysqlDataSource();
        dataSource_yyigou_ddc.setUrl(String.format("jdbc:mysql://%s:%s/%s", mysqlHost, mysqlPort, database_yyigou_ddc));
        dataSource_yyigou_ddc.setServerName(mysqlHost);
        dataSource_yyigou_ddc.setPortNumber(mysqlPort);
        dataSource_yyigou_ddc.setDatabaseName(database_yyigou_ddc);
        dataSource_yyigou_ddc.setUser(mysqlUsername);
        dataSource_yyigou_ddc.setPassword(mysqlPassword);

        MysqlDataSource dataSource_yyigou_dw = new MysqlDataSource();
        dataSource_yyigou_dw.setUrl(String.format("jdbc:mysql://%s:%s/%s", dorisHost, dorisPort, database_yyigou_dwd));
        dataSource_yyigou_dw.setServerName(dorisHost);
        dataSource_yyigou_dw.setPortNumber(dorisPort);
        dataSource_yyigou_dw.setDatabaseName(database_yyigou_dwd);
        dataSource_yyigou_dw.setUser(dorisUsername);
        dataSource_yyigou_dw.setPassword(dorisPassword);

        Map<String, MysqlDataSource> datasourceMap = new HashMap<>();
        datasourceMap.put(database_yyigou_dsrp, dataSource_yyigou_dsrp);
        datasourceMap.put(database_yyigou_ddc, dataSource_yyigou_ddc);
        datasourceMap.put(database_yyigou_dwd, dataSource_yyigou_dw);

        return datasourceMap;
    }

    private SchemaPlus buildSchema() throws Exception {
        Map<String, MysqlDataSource> datasource = getDatasource();

        Properties props = new Properties();
        Connection connection = DriverManager.getConnection("jdbc:calcite:", props);
        CalciteConnection calciteConn = connection.unwrap(CalciteConnection.class);
        SchemaPlus rootSchema = calciteConn.getRootSchema();

        SchemaPlus catalog = rootSchema.add(schema_catalog, new AbstractSchema());

        catalog.add(schema_yyigou_dsrp, JdbcSchema.create(
                catalog, schema_yyigou_dsrp, datasource.get(database_yyigou_dsrp), null, schema_yyigou_dsrp));

        catalog.add(schema_yyigou_ddc, JdbcSchema.create(
                catalog, schema_yyigou_ddc, datasource.get(database_yyigou_ddc), null, schema_yyigou_ddc));

        catalog.add(schema_yyigou_dwd, JdbcSchema.create(
                catalog, schema_yyigou_dwd, datasource.get(database_yyigou_dwd), null, schema_yyigou_dwd));

        return catalog;
    }

    private Planner buildPlanner(SchemaPlus catalog) {
        SqlOperatorTable chainedTable = SqlOperatorTables.chain(
                SqlStdOperatorTable.instance(),
                SqlLibraryOperatorTableFactory.INSTANCE.getOperatorTable(SqlLibrary.MYSQL)
        );

        FrameworkConfig frameworkConfig = Frameworks.newConfigBuilder()
                .parserConfig(SqlParser.configBuilder()
                        .setConformance(SqlConformanceEnum.MYSQL_5)
                        .setLex(Lex.MYSQL)
                        .setCaseSensitive(false)
                        .setIdentifierMaxLength(256)
                        .build())
                .operatorTable(chainedTable)
                .defaultSchema(catalog)
                .build();

        return Frameworks.getPlanner(frameworkConfig);
    }

    public List<ColumnBO> parseSQL(String rawSql) {
        return parseSQL(rawSql, false);
    }

    /**
     * 解析sql，获取sql中用到的字段信息
     *
     * @param rawSql
     * @return
     * @throws Exception
     */
    private List<ColumnBO> parseSQL(String rawSql, boolean warmup) {
        try {
            if (!warmup && !isWarmedUp.get()) {
                throw new BusinessException("未完成预热");
            }

            List<ColumnBO> result = new ArrayList<>();

            Map<String, Object> transResult = sqlTrans(rawSql);
            String sql = transResult.get("sql").toString();
            Map<String, String> unicodeToChineseMap = (Map<String, String>) transResult.get("unicodeToChineseMap");

            try (Planner planner = buildPlanner(catalogSchema)) {
                SqlNode parsed = planner.parse(sql);
                SqlNode validated = planner.validate(parsed);

                RelNode relNode = planner.rel(validated).project();

                Project project = findTopProject(relNode);
                if (project == null) {
                    throw new BusinessException("未找到顶层 Project 节点，可能是复杂结构");
                }

                List<RexNode> projections = project.getProjects();
                List<RelDataTypeField> outputFields = project.getRowType().getFieldList();
                List<RelDataTypeField> inputFields = project.getInput().getRowType().getFieldList();
                RelMetadataQuery mq = relNode.getCluster().getMetadataQuery();

                Set<String> tableNames = new HashSet<>();

                Map<String, TableMetaBO> tableMetaBOMap = schemaLoader.loadTableMeta(schema_yyigou_dsrp, schema_yyigou_ddc);
                Map<String, ColumnMetaBO> columnMetaBOMap = schemaLoader.loadColumnMeta(schema_yyigou_dsrp, schema_yyigou_ddc);

                Map<String, TableBO> tableBOMap = new HashMap<>();

                for (int i = 0; i < projections.size(); i++) {
                    RexNode rex = projections.get(i);
                    RelDataTypeField outputField = outputFields.get(i);

                    String outputFieldName = outputField.getName();
//            RelDataType type = outputField.getType();
//            String sqlTypeName = type.getSqlTypeName().getName();
//            Integer precision = type.getPrecision();
//            Integer scale = type.getScale();
//            boolean nullable = type.isNullable();
//            int idx = outputField.getIndex();

                    String sqlTypeName = "";
                    Integer precision = null;
                    Integer scale = null;
                    boolean nullable = false;
                    int idx = -1;
                    String columnKey = "";
                    String defaultValue = "";

                    Long length = null;
                    String comment = "";

                    String originName = "(表达式)";
                    String originTable = "(表达式)";
                    boolean renamed = false;

                    // 优先使用 RelMetadataQuery 进行字段来源追踪
                    Set<RelColumnOrigin> origins = mq.getColumnOrigins(relNode, i);
                    if (origins != null && !origins.isEmpty()) {
                        // 只取第一个来源（一般为唯一，如果是 JOIN，可遍历）
                        RelColumnOrigin origin = origins.iterator().next();
                        RelOptTable table = origin.getOriginTable();
                        originTable = String.join(".", table.getQualifiedName());

                        // 拿原始字段名
                        int originIndex = origin.getOriginColumnOrdinal();
                        if (originIndex >= 0 && originIndex < table.getRowType().getFieldCount()) {
                            originName = table.getRowType().getFieldList().get(originIndex).getName();
                            renamed = !originName.equalsIgnoreCase(outputFieldName);
                        }
                    }
                    // fallback：如果 metadata 无结果，并且是直接投影字段
                    else if (rex instanceof RexInputRef) {
                        RexInputRef ref = (RexInputRef) rex;
                        int index = ref.getIndex();

                        RelDataTypeField inputField = inputFields.get(index);
                        originName = inputField.getName();
                        renamed = !originName.equals(outputFieldName);
                        originTable = findTableByIndex(project.getInput(), index);
                    }

                    originName = restoreChinese(unicodeToChineseMap, originName);
                    outputFieldName = restoreChinese(unicodeToChineseMap, outputFieldName);
                    originTable = restoreChinese(unicodeToChineseMap, originTable);

                    // 使用时（结合你主循环中的 originTable 和 originName）：
                    String metaKey = originTable.substring(originTable.indexOf(".") + 1).toLowerCase() + "." + originName.toLowerCase();
                    ColumnMetaBO meta = columnMetaBOMap.get(metaKey);
                    if (meta != null) {
                        length = meta.getLength();
                        comment = meta.getComment();
                        sqlTypeName = meta.getColumnType();
                        precision = meta.getNumPrecision();
                        scale = meta.getNumScale();
                        nullable = meta.isNullable();
                        idx = meta.getColumnOrder();
                        defaultValue = meta.getDefaultValue();
                        columnKey = meta.getColumnKey();
                    } else {
                        throw new BusinessException(metaKey + ":字段未找到元信息");
                    }

                    tableNames.add(originTable);


                    ColumnBO columnBO = new ColumnBO();
//                columnBO.setSchemaName();
                    columnBO.setTableName(originTable);
                    columnBO.setColumnName(originName);
                    columnBO.setParsedColumnName(outputFieldName);
                    columnBO.setRenamed(renamed);
                    columnBO.setColumnType(sqlTypeName);
                    columnBO.setLength(length);
                    columnBO.setNumPrecision(precision);
                    columnBO.setNumScale(scale);
                    columnBO.setNullable(nullable);
                    columnBO.setDefaultValue(defaultValue);
                    columnBO.setComment(comment);
                    columnBO.setColumnOrder(idx);
                    columnBO.setColumnKey(columnKey);
//                columnBO.setTableBO();

                    result.add(columnBO);
                }

                for (String tableName : tableNames) {
                    TableMetaBO tableMetaBO = tableMetaBOMap.get(tableName.substring(tableName.indexOf(".") + 1));
                    TableBO tableBO = new TableBO();
                    tableBO.setSchemaName(tableMetaBO.getSchemaName());
                    tableBO.setTableName(tableMetaBO.getTableName());
                    tableBO.setComment(tableMetaBO.getComment());

                    tableBOMap.put(tableName, tableBO);
                }

                for (ColumnBO columnBO : result) {
                    TableBO tableBO = tableBOMap.get(columnBO.getTableName());
                    columnBO.setSchemaName(tableBO.getSchemaName());
                    columnBO.setTableName(tableBO.getTableName());

                    columnBO.setTableBO(tableBO);
                }
            }

            return result;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }


    private Project findTopProject(RelNode rel) {
        if (rel instanceof Project) {
            return (Project) rel;
        }
        for (RelNode input : rel.getInputs()) {
            Project result = findTopProject(input);
            if (result != null) {
                return result;
            }
        }
        return null;
    }

    private String findTableByIndex(RelNode input, int index) {
        if (input instanceof TableScan) {
            return String.join(".", ((TableScan) input).getTable().getQualifiedName());
        }

        List<RelNode> children = input.getInputs();
        int runningIndex = 0;

        for (RelNode child : children) {
            int fieldCount = child.getRowType().getFieldCount();
            if (index < runningIndex + fieldCount) {
                return findTableByIndex(child, index - runningIndex);
            }
            runningIndex += fieldCount;
        }

        return "(未知来源)";
    }


    // 替换中文为 __UNICODE_xxxx__ 格式
    private String replaceChinese(Map<String, String> unicodeToChineseMap, String input) {
        StringBuilder sb = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (Character.UnicodeBlock.of(c).toString().contains("CJK")) {
                String unicodeKey = String.format("__UNICODE_%04X__", (int) c);
                unicodeToChineseMap.put(unicodeKey, String.valueOf(c));
                sb.append(unicodeKey);
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    // 还原 __UNICODE_xxxx__ 为原始中文
    private String restoreChinese(Map<String, String> unicodeToChineseMap, String input) {
        String result = input;
        for (Map.Entry<String, String> entry : unicodeToChineseMap.entrySet()) {
            result = result.replace(entry.getKey(), entry.getValue());
        }
        return result;
    }
}
