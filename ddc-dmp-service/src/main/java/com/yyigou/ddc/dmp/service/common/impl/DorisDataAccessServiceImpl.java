package com.yyigou.ddc.dmp.service.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.yyigou.ddc.dmp.common.context.SqlExecuteContext;
import com.yyigou.ddc.dmp.common.util.SpELParserUtil;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.SqlColumnMetaBO;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.SqlFieldBO;
import com.yyigou.ddc.dmp.model.req.dataset.ModelGetReq;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRes;
import com.yyigou.ddc.dmp.service.common.DorisDataAccessService;
import com.yyigou.ddc.dmp.service.dataset.DataModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/22
 */
@Service
@Slf4j
public class DorisDataAccessServiceImpl implements DorisDataAccessService {

    // Doris 连接池注入
    @Autowired
    @Qualifier("dorisDataSource")
    private DataSource dataSource;

    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;

    @Value("${common.doris.odbc.database.name.prefix:dev_}")
    private String dorisDatabaseNamePrefix;

    @Autowired
    private DataModelService dataModelService;


    /**
     * 获取查询sql的中返回的字段信息（sql必须可执行）
     * @param selectSql
     * @return
     * @throws SQLException
     */
    @Override
    public List<SqlColumnMetaBO> extractColumnNamesFromSql(String selectSql) throws SQLException {
        List<SqlColumnMetaBO> columns = new ArrayList<>();
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(selectSql);
             ResultSet rs = stmt.executeQuery()) {

            // 获取结果集元数据
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            // 遍历所有列
            for (int i = 1; i <= columnCount; i++) {
                SqlColumnMetaBO sqlColumnMetaBO = new SqlColumnMetaBO();
//                sqlColumnMetaBO.setColumnName(metaData.getColumnName(i));
                // 如果有as，则返回as后的字段名
                sqlColumnMetaBO.setColumnName(metaData.getColumnLabel(i));
                sqlColumnMetaBO.setColumnType(metaData.getColumnTypeName(i));
                columns.add(sqlColumnMetaBO);
            }
        }
        return columns;
    }

    /**
     * 目前是定制化实现，只考虑底层已经是不需要分组的sql，直接变量替换后，执行sql拿到结果即可
     *
     * @param sqlExecuteContext
     * @return
     * @throws SQLException
     */
    @Override
    public List<Map<String, Object>> executeQuery(SqlExecuteContext sqlExecuteContext) throws SQLException {
        // todo-zyc
        // 判断数据集类型。目前仅支持底层是一段包含了变量 + CTE语法的 sql
        // 如果底层是CTE语法，先不拼接sql了。正常应该这段sql作为子查询，在外面拼接select、group、where等条件
        String sql = getSql(sqlExecuteContext);
        // 先是变量处理，集合改为字符串，并且所有的集合元素加上两边加上单引号，最后又去除两端的单引号。（帆软报表的sql就是这样的）
        processVariableMap(sqlExecuteContext);

        String processedSql = SpELParserUtil.parse(sql, sqlExecuteContext.getProcessedVariableMap(), String.class);
        log.warn("报表sql:\n {}",processedSql);

        // 直接执行，然后返回结果
        return dorisJdbcTemplate.queryForList(processedSql);
    }

    /**
     * 处理上下文中的变量
     * 1. 放入 enterpriseNo
     * 2. 集合类型改为字符串，并且所有的集合元素加上两边加上单引号，最后又去除两端的单引号。（帆软报表的sql就是这样的，本期保持一致）
     * @param sqlExecuteContext
     */
    private void processVariableMap(SqlExecuteContext sqlExecuteContext) {
        Map<String, Object> processedVariableMap = new HashMap<>();
        processedVariableMap.put("enterprise_no", sqlExecuteContext.getEnterpriseNo());
        processedVariableMap.put("dorisEnv", dorisDatabaseNamePrefix);
        if (CollectionUtil.isNotEmpty(sqlExecuteContext.getVariableMap())) {
            // 处理集合类型
            for (Map.Entry<String, Object> entry : sqlExecuteContext.getVariableMap().entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (value instanceof Collection<?>) {
                    // todo-zyc 是否需要处理特殊字符
                    Collection<?> collection = (Collection<?>) value;
                    if (!collection.isEmpty()) {
                        // 集合元素处理：每个元素加上单引号，并拼接成字符串
                        String joined = collection.stream()
                                .map(item -> "'" + item.toString() + "'")
                                .collect(Collectors.joining(","));
                        // 去除首尾单引号，例如：'A','B','C' -> A','B','C
                        joined = joined.substring(1, joined.length() - 1);
                        processedVariableMap.put(key, joined);
                    } else {
                        // 空列表可设为 null 或空字符串
                        processedVariableMap.put(key, null);
                    }
                } else {
                    // 非集合类型直接放入
                    processedVariableMap.put(key, value);
                }
            }

        }
        sqlExecuteContext.setProcessedVariableMap(processedVariableMap);
    }

    private String getSql(SqlExecuteContext sqlExecuteContext) {
        Optional<SqlFieldBO> first = sqlExecuteContext.getSelectList().stream().findFirst();
        if (!first.isPresent()) {
            throw new RuntimeException("sqlExecuteContext.getSelectList() is empty");
        }
        // todo-zyc 根据数据集，从模型层获取sql.暂时直接从模型层取

        ModelGetReq modelDetailGetReq = new ModelGetReq();
        modelDetailGetReq.setEnterpriseNo(sqlExecuteContext.getEnterpriseNo());
        modelDetailGetReq.setModelNo(first.get().getModelNo());
        ModelRes model = dataModelService.getModel(modelDetailGetReq);
        return model.getSqlView();
    }

}
