package com.yyigou.ddc.dmp.service.comparemodel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelCalMetric;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
public interface CompareModelCalMetricService extends IService<CompareModelCalMetric> {
    @Transactional(rollbackFor = Exception.class)
    void saveModelRefCalMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelCalMetric> existingCalMetricMap);

    @Transactional(rollbackFor = Exception.class)
    void updateModelRefCalMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel);

    void fillCalculatedMetricList(CompareModelGetRes compareModelGetRes, CompareModel compareModel);
}
