package com.yyigou.ddc.dmp.service.sqlextractor.impl;

import com.yyigou.ddc.dmp.dao.SqlExtractor;
import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnBO;
import com.yyigou.ddc.dmp.service.sqlextractor.SqlExtractorService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SqlExtractorServiceImpl implements SqlExtractorService {
    @Resource
    private SqlExtractor sqlExtractor;

    @Override
    public List<ColumnBO> extractTableInfoFromSql(String sql) {
        return sqlExtractor.parseSQL(sql);
    }
}
