package com.yyigou.ddc.dmp.service.dataset;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetPreviewReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetValidateRes;

public interface DatasetService extends IService<Dataset> {
    String saveDataset(DatasetSaveReq datasetSaveReq);

    DatasetDetailRes getDataset(DatasetGetReq datasetGetReq);

    /**
     * 预览数据集数据
     *
     * @param datasetPreviewReq 预览请求参数
     * @return 预览数据结果
     */
    DatasetPreviewRes previewData(DatasetPreviewReq datasetPreviewReq);
}