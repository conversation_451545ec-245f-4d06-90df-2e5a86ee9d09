package com.yyigou.ddc.dmp.service.meta;

import com.yyigou.ddc.dmp.model.res.meta.ColumnsRes;
import com.yyigou.ddc.dmp.model.res.meta.SchemataRes;
import com.yyigou.ddc.dmp.model.res.meta.TablesRes;

import java.util.List;

public interface MetaService {
    List<SchemataRes> loadSchemata();

    List<TablesRes> loadTables(String schemaName);

    List<ColumnsRes> loadColumns(String schemaName, String tableName);

}
