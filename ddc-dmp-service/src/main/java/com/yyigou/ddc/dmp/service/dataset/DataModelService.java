package com.yyigou.ddc.dmp.service.dataset;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.entity.DataModel;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.res.dataset.ModelFieldRes;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRefRes;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRes;

import java.util.List;
import java.util.Map;

public interface DataModelService extends IService<DataModel> {
    String saveModel(ModelSaveReq modelSaveReq);

    ModelRes getModel(ModelGetReq modelGetReq);

    ModelFieldRes getModelField(ModelFieldGetReq modelFieldGetReq);

    String saveModelRef(ModelRefSaveReq modelRefSaveReq);

    Map<String, List<ModelRefRes>> getModelRef(ModelRefGetReq modelRefGetReq);
}