package com.yyigou.ddc.dmp.service.comparemodel.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.enums.CompareModelMerticTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelMetric;
import com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelMetricMapper;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelRefBaselineMetricReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelRefMetricReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefBaselineMetricRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefMetricRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelMetricService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Service
public class CompareModelMetricServiceImpl extends DmpServiceImpl<CompareModelMetricMapper, CompareModelMetric> implements CompareModelMetricService {


    /**
     * 保存 比对指标/基准指标
     * @param compareModelSaveReq
     * @param compareModel
     * @param existingMetricMap 已存在的指标映射，为null时表示新增场景
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveModelRefMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelMetric> existingMetricMap) {
        if (existingMetricMap == null) {
            existingMetricMap = new HashMap<>();
        }

        List<CompareModelMetric> saveList = new ArrayList<>();
        List<CompareModelMetric> updateList = new ArrayList<>();

        List<CompareModelMetric> compareModelMetricList = new ArrayList<>();
        int sort = 1;
        for (CompareModelRefMetricReq compareMetricReq : compareModelSaveReq.getCompareMetricList()) {
            CompareModelMetric compareModelMetric = BeanCopyUtil.copyFields(compareMetricReq, CompareModelMetric.class);
            compareModelMetric.setCompareModelNo(compareModel.getModelNo());
            compareModelMetric.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
            compareModelMetric.setMetricNo(compareMetricReq.getMetricNo());
            compareModelMetric.setMetricType(CompareModelMerticTypeEnum.COMPARE_METRIC.getValue());
            compareModelMetric.setDatasetNo(compareModel.getCompareDatasetNo());
            compareModelMetric.setSort(sort++);
            if (determineSaveOrUpdateAction(compareModelMetric, existingMetricMap, saveList, updateList)) {
                // 新增记录的no回写到Req中，这样后续保存查询条件时才可以获取到
                compareMetricReq.setCompareModelMetricNo(compareModelMetric.getCompareModelMetricNo());
            }
            compareModelMetricList.add(compareModelMetric);
        }

        sort = 1;
        if (CollectionUtil.isNotEmpty(compareModelSaveReq.getBaselineMetricList())) {
            for (CompareModelRefBaselineMetricReq baselineMetricReq : compareModelSaveReq.getBaselineMetricList()) {
                CompareModelMetric compareModelMetric = BeanCopyUtil.copyFields(baselineMetricReq, CompareModelMetric.class);
                compareModelMetric.setCompareModelNo(compareModel.getModelNo());
                compareModelMetric.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
                compareModelMetric.setMetricNo(baselineMetricReq.getMetricNo());
                compareModelMetric.setMetricType(CompareModelMerticTypeEnum.BASELINE_METRIC.getValue());
                compareModelMetric.setDatasetNo(baselineMetricReq.getDatasetNo());
                compareModelMetric.setSort(sort++);
                if (determineSaveOrUpdateAction(compareModelMetric, existingMetricMap, saveList, updateList)) {
                    baselineMetricReq.setCompareModelMetricNo(compareModelMetric.getCompareModelMetricNo());
                }
                compareModelMetricList.add(compareModelMetric);
            }
        }

        this.saveBatch(saveList);
        this.updateBatchByIdAndEnterpriseNo(updateList);

        // 处理需要删除的指标
        if (!existingMetricMap.isEmpty()) {
            Collection<CompareModelMetric> needDeleteMetrics = existingMetricMap.values();
            for (CompareModelMetric needDeleteMetric : needDeleteMetrics) {
                needDeleteMetric.setDeleted(DeletedEnum.DELETED.getValue());
            }
            this.updateBatchByIdAndEnterpriseNo(needDeleteMetrics);
        }
    }

    public boolean determineSaveOrUpdateAction(CompareModelMetric metric, Map<String, CompareModelMetric> existingMetricMap, List<CompareModelMetric> saveList, List<CompareModelMetric> updateList) {
        if (StrUtil.isNotEmpty(metric.getCompareModelMetricNo()) && existingMetricMap.containsKey(metric.getCompareModelMetricNo())) {
            // 更新场景
            CompareModelMetric existingMetric = existingMetricMap.remove(metric.getCompareModelMetricNo());
            metric.setId(existingMetric.getId());
            updateList.add(metric);
            return false;
        } else {
            // 新增场景
            metric.setCompareModelMetricNo(IdUtil.objectId());
            saveList.add(metric);
            return true;
        }
    }

    /**
     * 更新比对指标/基准指标
     * @param compareModelSaveReq
     * @param compareModel
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateModelRefMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel) {
        List<CompareModelMetric> dbModelMetricList = this.list(Wrappers.lambdaQuery(CompareModelMetric.class)
                .eq(CompareModelMetric::getCompareModelNo, compareModel.getModelNo())
                .eq(CompareModelMetric::getEnterpriseNo, compareModel.getEnterpriseNo())
                .eq(CompareModelMetric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, CompareModelMetric> existingMetricMap = dbModelMetricList.stream()
                .collect(Collectors.toMap(CompareModelMetric::getCompareModelMetricNo, Function.identity()));

        this.saveModelRefMetric(compareModelSaveReq, compareModel, existingMetricMap);
    }


    /**
     * 填充指标信息（比对指标和基准指标）
     * @param compareModelGetRes 返回结果对象
     * @param compareModel 主模型对象
     */
    @Override
    public void fillMetricList(CompareModelGetRes compareModelGetRes, CompareModel compareModel) {
        List<CompareModelMetric> metricList = this.list(
                Wrappers.lambdaQuery(CompareModelMetric.class)
                        .eq(CompareModelMetric::getCompareModelNo, compareModel.getModelNo())
                        .eq(CompareModelMetric::getEnterpriseNo, compareModel.getEnterpriseNo())
                        .eq(CompareModelMetric::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .orderByAsc(CompareModelMetric::getSort)
        );

        if (CollectionUtils.isNotEmpty(metricList)) {
            // 分离比对指标和基准指标
            List<CompareModelMetric> compareMetricList = metricList.stream()
                    .filter(metric -> CompareModelMerticTypeEnum.COMPARE_METRIC.getValue().equals(metric.getMetricType()))
                    .collect(Collectors.toList());

            List<CompareModelMetric> baselineMetricList = metricList.stream()
                    .filter(metric -> CompareModelMerticTypeEnum.BASELINE_METRIC.getValue().equals(metric.getMetricType()))
                    .collect(Collectors.toList());

            // 转换并设置比对指标
            if (CollectionUtils.isNotEmpty(compareMetricList)) {
                List<CompareModelRefMetricRes> compareMetricResList = BeanCopyUtil.copyFieldsList(compareMetricList, CompareModelRefMetricRes.class);
                compareModelGetRes.setCompareMetricList(compareMetricResList);
            }

            // 转换并设置基准指标
            if (CollectionUtils.isNotEmpty(baselineMetricList)) {
                List<CompareModelRefBaselineMetricRes> baselineMetricResList = BeanCopyUtil.copyFieldsList(baselineMetricList, CompareModelRefBaselineMetricRes.class);
                compareModelGetRes.setBaselineMetricList(baselineMetricResList);
            }
        }
    }

}
