package com.yyigou.ddc.dmp.service.comparemodel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelDim;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
public interface CompareModelDimService extends IService<CompareModelDim> {

    @Transactional(rollbackFor = Exception.class)
    void saveModelRefDim(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelDim> existingDimMap);

    void updateModelRefDim(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel);

    void fillDimList(CompareModelGetRes compareModelGetRes, CompareModel compareModel);
}
