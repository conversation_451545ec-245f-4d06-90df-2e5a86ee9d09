package com.yyigou.ddc.dmp.service.comparemodel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelMetric;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
public interface CompareModelMetricService extends IService<CompareModelMetric> {
    @Transactional(rollbackFor = Exception.class)
    void saveModelRefMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelMetric> existingMetricMap);

    @Transactional(rollbackFor = Exception.class)
    void updateModelRefMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel);

    void fillMetricList(CompareModelGetRes compareModelGetRes, CompareModel compareModel);
}
