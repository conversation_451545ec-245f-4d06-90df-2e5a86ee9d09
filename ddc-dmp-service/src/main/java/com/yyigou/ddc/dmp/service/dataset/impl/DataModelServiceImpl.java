package com.yyigou.ddc.dmp.service.dataset.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.entity.DataModel;
import com.yyigou.ddc.dmp.dao.entity.DataModelField;
import com.yyigou.ddc.dmp.dao.entity.DataModelFieldRef;
import com.yyigou.ddc.dmp.dao.entity.DataModelRef;
import com.yyigou.ddc.dmp.dao.mapper.DataModelFieldMapper;
import com.yyigou.ddc.dmp.dao.mapper.DataModelFieldRefMapper;
import com.yyigou.ddc.dmp.dao.mapper.DataModelMapper;
import com.yyigou.ddc.dmp.dao.mapper.DataModelRefMapper;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.res.dataset.ModelFieldRefRes;
import com.yyigou.ddc.dmp.model.res.dataset.ModelFieldRes;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRefRes;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRes;
import com.yyigou.ddc.dmp.service.dataset.DataModelService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataModelServiceImpl extends ServiceImpl<DataModelMapper, DataModel> implements DataModelService {
    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private DataModelRefMapper dataModelRefMapper;

    @Resource
    private DataModelFieldRefMapper dataModelFieldRefMapper;

    @Override
    @Transactional
    public String saveModel(ModelSaveReq modelSaveReq) {
        if (CollectionUtils.isEmpty(modelSaveReq.getModelFieldSaveList())) {
            throw new BusinessException("模型字段不能为空");
        }

        DataModel dataModel = BeanCopyUtil.copyFieldsByJson(modelSaveReq, DataModel.class);
        if (StringUtils.isEmpty(dataModel.getModelNo())) {
            //新增
            //TODO shenbin 改成规则生成器
            dataModel.setModelNo(UUID.fastUUID().toString());
            save(dataModel);

            List<DataModelField> fields = modelSaveReq.getModelFieldSaveList().stream().map(fieldSaveReq -> {
                DataModelField dataModelField = BeanCopyUtil.copyFieldsByJson(fieldSaveReq, DataModelField.class);
                dataModelField.setModelNo(dataModel.getModelNo());
                dataModelField.setEnterpriseNo(dataModel.getEnterpriseNo());
                return dataModelField;
            }).collect(Collectors.toList());

            dataModelFieldMapper.insert(fields);
        } else {
            boolean exists = exists(Wrappers.<DataModel>lambdaQuery()
                    .eq(DataModel::getEnterpriseNo, dataModel.getEnterpriseNo())
                    .eq(DataModel::getModelNo, dataModel.getModelNo())
                    .eq(DataModel::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("模型不存在");
            }

            DataModel toDeleteDataModel = new DataModel();
            toDeleteDataModel.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteDataModel, Wrappers.lambdaUpdate(DataModel.class)
                    .eq(DataModel::getEnterpriseNo, dataModel.getEnterpriseNo())
                    .eq(DataModel::getModelNo, dataModel.getModelNo())
                    .set(DataModel::getDeleted, DeletedEnum.UN_DELETE)
            );
            save(dataModel);

            DataModelField toDeleteDataModelField = new DataModelField();
            toDeleteDataModelField.setDeleted(DeletedEnum.DELETED.getValue());
            dataModelFieldMapper.update(toDeleteDataModelField, Wrappers.lambdaQuery(DataModelField.class)
                    .eq(DataModelField::getEnterpriseNo, dataModel.getEnterpriseNo())
                    .eq(DataModelField::getModelNo, dataModel.getModelNo())
                    .eq(DataModelField::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            List<DataModelField> fields = modelSaveReq.getModelFieldSaveList().stream().map(fieldSaveReq -> {
                DataModelField dataModelField = BeanCopyUtil.copyFieldsByJson(fieldSaveReq, DataModelField.class);
                dataModelField.setModelNo(dataModel.getModelNo());
                dataModelField.setEnterpriseNo(dataModel.getEnterpriseNo());
                return dataModelField;
            }).collect(Collectors.toList());

            dataModelFieldMapper.insert(fields);
        }

        return dataModel.getModelNo();
    }

    @Override
    public ModelRes getModel(ModelGetReq modelGetReq) {
        DataModel dataModel = getOne(Wrappers.<DataModel>lambdaQuery()
                .eq(DataModel::getEnterpriseNo, modelGetReq.getEnterpriseNo())
                .eq(DataModel::getModelNo, modelGetReq.getModelNo())
                .eq(DataModel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataModel) {
            throw new BusinessException("模型不存在");
        }

        List<DataModelField> dataModelFields = dataModelFieldMapper.selectList(Wrappers.<DataModelField>lambdaQuery()
                .eq(DataModelField::getEnterpriseNo, modelGetReq.getEnterpriseNo())
                .eq(DataModelField::getModelNo, modelGetReq.getModelNo())
                .eq(DataModelField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        ModelRes modelRes = BeanCopyUtil.copyFields(dataModel, ModelRes.class);

        if (CollectionUtils.isNotEmpty(dataModelFields)) {
            List<ModelFieldRes> modelFieldRes = BeanCopyUtil.copyFieldsList(dataModelFields, ModelFieldRes.class);
            modelRes.setModelFieldList(modelFieldRes);
        }

        return modelRes;
    }

    @Override
    public ModelFieldRes getModelField(ModelFieldGetReq modelFieldGetReq) {
        DataModelField dataModelField = dataModelFieldMapper.selectOne(Wrappers.<DataModelField>lambdaQuery()
                .eq(DataModelField::getEnterpriseNo, modelFieldGetReq.getEnterpriseNo())
                .eq(DataModelField::getModelNo, modelFieldGetReq.getModelNo())
                .eq(DataModelField::getFieldCode, modelFieldGetReq.getFieldCode())
                .eq(DataModelField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataModelField) {
            throw new BusinessException("模型字段不存在");
        }

        DataModel dataModel = getOne(Wrappers.<DataModel>lambdaQuery()
                .eq(DataModel::getEnterpriseNo, dataModelField.getEnterpriseNo())
                .eq(DataModel::getModelNo, dataModelField.getModelNo())
                .eq(DataModel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataModel) {
            throw new BusinessException("模型不存在");
        }

        ModelFieldRes modelFieldRes = BeanCopyUtil.copyFields(dataModelField, ModelFieldRes.class);

        ModelRes modelRes = BeanCopyUtil.copyFields(dataModel, ModelRes.class);
        modelFieldRes.setModelRes(modelRes);

        return modelFieldRes;
    }

    @Override
    @Transactional
    public String saveModelRef(ModelRefSaveReq modelRefSaveReq) {
        if (CollectionUtils.isEmpty(modelRefSaveReq.getModelFieldRefSaveReqList())) {
            throw new BusinessException("模型字段关系不能为空");
        }

        DataModelRef dataModelRef = BeanCopyUtil.copyFieldsByJson(modelRefSaveReq, DataModelRef.class);
        if (StringUtils.isEmpty(dataModelRef.getDataModelRefNo())) {
            //新增
            //TODO shenbin 改成规则生成器
            dataModelRef.setDataModelRefNo(UUID.fastUUID().toString());
            dataModelRefMapper.insert(dataModelRef);

            List<DataModelFieldRef> dataModelFieldRefs = BeanCopyUtil.copyFieldsList(modelRefSaveReq.getModelFieldRefSaveReqList(), DataModelFieldRef.class);
            dataModelFieldRefMapper.insert(dataModelFieldRefs);
        } else {
            DataModelRef dataModelRefInDb = dataModelRefMapper.selectOne(Wrappers.<DataModelRef>lambdaQuery()
                    .eq(DataModelRef::getEnterpriseNo, modelRefSaveReq.getEnterpriseNo())
                    .eq(DataModelRef::getDataModelRefNo, modelRefSaveReq.getDataModelRefNo())
                    .eq(DataModelRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (null == dataModelRefInDb) {
                throw new BusinessException("模型关系不存在");
            }

            dataModelRefInDb.setDeleted(DeletedEnum.DELETED.getValue());
            dataModelRefMapper.update(dataModelRefInDb, Wrappers.lambdaQuery(DataModelRef.class)
                    .eq(DataModelRef::getEnterpriseNo, dataModelRefInDb.getEnterpriseNo())
                    .eq(DataModelRef::getDataModelRefNo, dataModelRefInDb.getDataModelRefNo())
                    .eq(DataModelRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            dataModelRefMapper.insert(dataModelRef);

            DataModelFieldRef toDeleteDataModelFieldRef = new DataModelFieldRef();
            toDeleteDataModelFieldRef.setDeleted(DeletedEnum.DELETED.getValue());
            dataModelFieldRefMapper.update(toDeleteDataModelFieldRef, Wrappers.lambdaQuery(DataModelFieldRef.class)
                    .eq(DataModelFieldRef::getEnterpriseNo, dataModelRefInDb.getEnterpriseNo())
                    .eq(DataModelFieldRef::getDataModelRefNo, dataModelRefInDb.getDataModelRefNo())
                    .eq(DataModelFieldRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            List<DataModelFieldRef> dataModelFieldRefs = BeanCopyUtil.copyFieldsList(modelRefSaveReq.getModelFieldRefSaveReqList(), DataModelFieldRef.class);
            dataModelFieldRefs.forEach(dataModelFieldRef -> {
                dataModelFieldRef.setDataModelRefNo(dataModelRef.getDataModelRefNo());
                dataModelFieldRef.setEnterpriseNo(dataModelRef.getEnterpriseNo());
            });
            dataModelFieldRefMapper.insert(dataModelFieldRefs);
        }

        return dataModelRef.getDataModelRefNo();
    }

    @Override
    public Map<String, List<ModelRefRes>> getModelRef(ModelRefGetReq modelRefGetReq) {
        Map<String, List<ModelRefRes>> result = new HashMap<>();

        {
            List<DataModelRef> dataModelRefList = dataModelRefMapper.selectList(Wrappers.<DataModelRef>lambdaQuery()
                    .eq(DataModelRef::getEnterpriseNo, modelRefGetReq.getEnterpriseNo())
                    .eq(DataModelRef::getSourceModelNo, modelRefGetReq.getModelNo())
                    .eq(DataModelRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (CollectionUtils.isNotEmpty(dataModelRefList)) {
                List<ModelRefRes> sourceList = new ArrayList<>();
                Set<String> refNos = dataModelRefList.stream().map(DataModelRef::getDataModelRefNo).collect(Collectors.toSet());
                List<DataModelFieldRef> dataModelFieldRefs = dataModelFieldRefMapper.selectList(Wrappers.<DataModelFieldRef>lambdaQuery()
                        .eq(DataModelFieldRef::getEnterpriseNo, modelRefGetReq.getEnterpriseNo())
                        .eq(DataModelFieldRef::getDataModelRefNo, refNos)
                        .eq(DataModelFieldRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
                );

                Map<String, List<DataModelFieldRef>> refNo2FieldRefs = new HashMap<>();
                if (CollectionUtils.isNotEmpty(dataModelFieldRefs)) {
                    refNo2FieldRefs.putAll(dataModelFieldRefs.stream().collect(Collectors.groupingBy(DataModelFieldRef::getDataModelRefNo)));
                }

                for (DataModelRef dataModelRef : dataModelRefList) {
                    ModelRefRes modelRefRes = BeanCopyUtil.copyFields(dataModelRef, ModelRefRes.class);
                    List<DataModelFieldRef> dataModelFieldRefList = refNo2FieldRefs.get(dataModelRef.getDataModelRefNo());
                    if (CollectionUtils.isNotEmpty(dataModelFieldRefList)) {
                        List<ModelFieldRefRes> modelFieldRefRes = BeanCopyUtil.copyFieldsList(dataModelFieldRefList, ModelFieldRefRes.class);
                        modelRefRes.setModelFieldRefResList(modelFieldRefRes);
                    }
                    sourceList.add(modelRefRes);
                }

                result.put("source", sourceList);
            }
        }

        {
            List<DataModelRef> dataModelRefList = dataModelRefMapper.selectList(Wrappers.<DataModelRef>lambdaQuery()
                    .eq(DataModelRef::getEnterpriseNo, modelRefGetReq.getEnterpriseNo())
                    .eq(DataModelRef::getTargetModelNo, modelRefGetReq.getModelNo())
                    .eq(DataModelRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (CollectionUtils.isNotEmpty(dataModelRefList)) {
                List<ModelRefRes> targetList = new ArrayList<>();
                Set<String> refNos = dataModelRefList.stream().map(DataModelRef::getDataModelRefNo).collect(Collectors.toSet());
                List<DataModelFieldRef> dataModelFieldRefs = dataModelFieldRefMapper.selectList(Wrappers.<DataModelFieldRef>lambdaQuery()
                        .eq(DataModelFieldRef::getEnterpriseNo, modelRefGetReq.getEnterpriseNo())
                        .eq(DataModelFieldRef::getDataModelRefNo, refNos)
                        .eq(DataModelFieldRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
                );

                Map<String, List<DataModelFieldRef>> refNo2FieldRefs = new HashMap<>();
                if (CollectionUtils.isNotEmpty(dataModelFieldRefs)) {
                    refNo2FieldRefs.putAll(dataModelFieldRefs.stream().collect(Collectors.groupingBy(DataModelFieldRef::getDataModelRefNo)));
                }

                for (DataModelRef dataModelRef : dataModelRefList) {
                    ModelRefRes modelRefRes = BeanCopyUtil.copyFields(dataModelRef, ModelRefRes.class);
                    List<DataModelFieldRef> dataModelFieldRefList = refNo2FieldRefs.get(dataModelRef.getDataModelRefNo());
                    if (CollectionUtils.isNotEmpty(dataModelFieldRefList)) {
                        List<ModelFieldRefRes> modelFieldRefRes = BeanCopyUtil.copyFieldsList(dataModelFieldRefList, ModelFieldRefRes.class);
                        modelRefRes.setModelFieldRefResList(modelFieldRefRes);
                    }
                    targetList.add(modelRefRes);
                }

                result.put("target", targetList);

            }
        }

        return result;
    }
}
