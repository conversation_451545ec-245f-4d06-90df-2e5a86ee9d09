package com.yyigou.ddc.dmp.service.dataset;

import com.yyigou.ddc.dmp.model.req.dataset.DatasetPreviewReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetValidateRes;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据集服务测试类
 */
@SpringBootTest
@SpringJUnitConfig
public class DatasetServiceTest {

    @Resource
    private DatasetService datasetService;

    /**
     * 测试数据预览功能
     */
    @Test
    public void testPreviewData() {
        DatasetPreviewReq request = new DatasetPreviewReq();
        request.setEnterpriseNo("2000002");
        request.setDatasetNo("test-dataset-001");
        request.setLimit(10);

        try {
            DatasetPreviewRes response = datasetService.previewData(request);
            System.out.println("预览数据测试结果:");
            System.out.println("数据集编号: " + response.getDatasetNo());
            System.out.println("数据条数: " + (response.getData() != null ? response.getData().size() : 0));
            System.out.println("字段数量: " + (response.getColumns() != null ? response.getColumns().size() : 0));
        } catch (Exception e) {
            System.out.println("预览数据测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试数据集校验功能
     */
    @Test
    public void testValidateDataset() {
        DatasetValidateReq request = new DatasetValidateReq();
        request.setEnterpriseNo("2000002");
        request.setDatasetNo("test-dataset-001");
        request.setDrivingSchemaName("test_schema");
        request.setDrivingTableName("test_table");

        // 添加关联表配置
        List<DatasetValidateReq.JoinTableConfig> joinTables = new ArrayList<>();
        DatasetValidateReq.JoinTableConfig joinTable = new DatasetValidateReq.JoinTableConfig();
        joinTable.setSchemaName("test_schema");
        joinTable.setTableName("test_join_table");
        joinTable.setJoinType("innerJoin");

        // 添加关联条件
        List<DatasetValidateReq.JoinCondition> joinConditions = new ArrayList<>();
        DatasetValidateReq.JoinCondition condition = new DatasetValidateReq.JoinCondition();
        condition.setLeftField("t0.id");
        condition.setRightField("t1.ref_id");
        condition.setOperator("=");
        joinConditions.add(condition);
        joinTable.setJoinConditions(joinConditions);

        joinTables.add(joinTable);
        request.setJoinTables(joinTables);

        try {
            DatasetValidateRes response = datasetService.validateDataset(request);
            System.out.println("数据集校验测试结果:");
            System.out.println("数据集编号: " + response.getDatasetNo());
            System.out.println("校验结果: " + (response.getValid() ? "通过" : "失败"));
            System.out.println("错误数量: " + (response.getErrors() != null ? response.getErrors().size() : 0));
            System.out.println("有效字段数量: " + (response.getValidFields() != null ? response.getValidFields().size() : 0));

            if (response.getErrors() != null && !response.getErrors().isEmpty()) {
                System.out.println("错误详情:");
                for (DatasetValidateRes.ValidationError error : response.getErrors()) {
                    System.out.println("- " + error.getErrorType() + ": " + error.getMessage());
                }
            }
        } catch (Exception e) {
            System.out.println("数据集校验测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
