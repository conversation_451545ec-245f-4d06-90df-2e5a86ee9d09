# 比价二期（数据集）详细设计

# ER 图

![比价-er[er]-2025820131920.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/v9kqDej8v5ay3OVx/img/1c9ac845-1675-47d3-8a9f-f4e7568ada92.png)

# 表结构

```mysql
CREATE TABLE `dmp_analysis_subject`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no` varchar(64)  NOT NULL COMMENT '租户编号',
    `subject_no`    varchar(64)  NOT NULL COMMENT '分析主题唯一标识',
    `subject_name`  varchar(255) NOT NULL COMMENT '分析主题名称',
    `description`   text COMMENT '主题描述',
    `status`        tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`       tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`     varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`   varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`   varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`     varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`   varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`   varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY             `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='分析主题表';

CREATE TABLE `dmp_analysis_subject_dataset_ref`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no` varchar(64) NOT NULL COMMENT '租户编号',
    `subject_no`    varchar(64) NOT NULL COMMENT '分析主题编号',
    `dataset_no`    varchar(64) NOT NULL COMMENT '数据集编号',
    `status`        tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`       tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`     varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`   varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`   varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`     varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`   varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`   varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY             `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='分析主题与数据集关系表';

CREATE TABLE `dmp_dataset`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`       varchar(64)  NOT NULL COMMENT '租户编号',
    `dataset_no`          varchar(64)  NOT NULL COMMENT '数据集唯一标识',
    `dataset_code`        varchar(255) NOT NULL COMMENT '数据集编码',
    `dataset_name`        varchar(255) NOT NULL COMMENT '数据集名称',
    `driving_schema_name` varchar(255) NOT NULL COMMENT '驱动表schema',
    `driving_table_name`  varchar(255) NOT NULL COMMENT '驱动表',
    `description`         text COMMENT '数据集描述',
    `status`              tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`             tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`           varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`         varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`         varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`           varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`         varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`         varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`        timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集表';

CREATE TABLE `dmp_dataset_join_rel`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`      varchar(64)  NOT NULL COMMENT '租户编号',
    `dataset_no`         varchar(64)  NOT NULL COMMENT '数据集编号',
    `source_schema_name` varchar(255) NOT NULL COMMENT '源表schema',
    `source_table_name`  varchar(255) NOT NULL COMMENT '源表',
    `target_schema_name` varchar(255) NOT NULL COMMENT '目标表schema',
    `target_table_name`  varchar(255) NOT NULL COMMENT '目标表',
    `join_type`          varchar(32)  NOT NULL COMMENT '关联关系：innerjoin, leftjoin, rightjoin',
    `join_condition`     text         NOT NULL COMMENT '关联条件',
    `status`             tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`            tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`          varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`        varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`        varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`          varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`        varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`        varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`       timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                  `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集join关系表';

CREATE TABLE `dmp_dataset_field`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`    varchar(64)  NOT NULL COMMENT '租户编号',
    `dataset_no`       varchar(64)  NOT NULL COMMENT '数据集编号',
    `dataset_field_no` varchar(64)  NOT NULL COMMENT '数据集字段编号',
    `schema_name`      varchar(255) NOT NULL COMMENT '字段来源表schema',
    `table_name`       varchar(255) NOT NULL COMMENT '字段来源表',
    `field_code`       varchar(255) NOT NULL COMMENT '字段编码',
    `field_name`       varchar(255) NOT NULL COMMENT '字段名称',
    `description`      text COMMENT '数据集字段描述',
    `status`           tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`          tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`        varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`      varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`      varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`        varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`      varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`      varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`     timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集字段表';

CREATE TABLE `dmp_dimension`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`  varchar(64)  NOT NULL COMMENT '租户编号',
    `dimension_no`   varchar(100) NOT NULL COMMENT '维度编号',
    `dimension_code` varchar(255) NOT NULL COMMENT '维度编码',
    `dimension_name` varchar(255) NOT NULL COMMENT '维度名称',
    `schema_name`    varchar(255) NOT NULL COMMENT '字段来源表schema',
    `table_name`     varchar(255) NOT NULL COMMENT '字段来源表',
    `description`    text COMMENT '维度描述',
    `status`         tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`        tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`      varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`    varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`    varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`      varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`    varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`    varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`   timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY              `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='维度表';

CREATE TABLE `dmp_dimension_field`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`  varchar(64)  NOT NULL COMMENT '租户编号',
    `dimension_no`   varchar(100) NOT NULL COMMENT '维度编号',
    `field_code`     varchar(255) NOT NULL COMMENT '模型字段',
    `field_semantic` varchar(32)  NOT NULL COMMENT '字段语义：business_key，display_name，description_only',
    `description`    text COMMENT '维度描述',
    `display_order`  int(11) NOT NULL DEFAULT '1' COMMENT '展示顺序',
    `date_format`    varchar(32) NULL COMMENT '日期格式',
    `status`         tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`        tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`      varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`    varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`    varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`      varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`    varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`    varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`   timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY              `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='维度字段表';

CREATE TABLE `dmp_dataset_dimension_ref`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`    varchar(64) NOT NULL COMMENT '租户编号',
    `dataset_no`       varchar(64) NOT NULL COMMENT '所属数据集编号',
    `dataset_field_no` varchar(64) NOT NULL COMMENT '数据集字段编号',
    `dimension_no`     varchar(64) NOT NULL COMMENT '维度编号',
    `description`      text COMMENT '维度描述',
    `status`           tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`          tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`        varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`      varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`      varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`        varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`      varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`      varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`     timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集字段和维度关联表';

CREATE TABLE `dmp_metric`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`    varchar(64)  NOT NULL COMMENT '租户编号',
    `metric_no`        varchar(64)  NOT NULL COMMENT '维度编号',
    `metric_code`      varchar(255) NOT NULL COMMENT '维度编码',
    `metric_name`      varchar(255) NOT NULL COMMENT '维度名称',
    `unit`             varchar(255) DEFAULT NULL COMMENT '单位',
    `num_precision`    varchar(255) DEFAULT NULL COMMENT '精度',
    `display_format`   varchar(255) DEFAULT NULL COMMENT '展示格式',
    `description`      text COMMENT '指标描述',
    `dataset_no`       varchar(64)  NOT NULL COMMENT '所属数据集编号',
    `dataset_field_no` varchar(64)  NOT NULL COMMENT '数据集字段编号',
    `agg_type`         varchar(32)  DEFAULT NULL COMMENT '聚合方式',
    `status`           tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`          tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`        varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`      varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`      varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`        varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`      varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`      varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`     timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='指标表';
```

# 数仓管理

## 同步链路

mysql 直接同步至 doris.dwd 层

## 同步策略

海豚调度：一天同步一次，每日凌晨 1 点开始同步全量数据

dbt：table\_duplicate 模式，数据全量同步到 doris 临时表，最后全量替换旧表

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/v9kqDej8v5ay3OVx/img/cf34f5fe-0b6f-41fc-b54e-0d5a308fb74b.png)

## 性能评估

*   [ ] 线上环境待评估
    

## 同步脚本

### 采购协议

```mysql
{{
    table_create_config(
        materialized_value="table_duplicate",
        duplicate_key_value=["enterprise_no"],
        distributed_by_value=["enterprise_no"]
    )
}}

WITH
-- 第一层：基础数据预过滤和准备
-- 1. 产品维度数据（预处理所有产品信息）
    goods_info AS (
        SELECT
            g.enterprise_no,
            g.sku_code,
            g.goods_name,
            g.register_code,
            g.factory_no,
            g.brand_no,
            g.category_no,
            g.common_name,
            g.instrument_model,
            g.spec,
            g.is_virtual,
            g.virtual_type,
            c.category_name,
            b.brand_name,
            f.company_name AS factory_name
        FROM {{ ods_source("mysql_rds_dsrp", "bdc_goods", "") }} g
                 INNER JOIN {{ ods_source("mysql_rds_dsrp", "bdc_brand", "") }} b
                            ON g.enterprise_no = b.enterprise_no AND g.brand_no = b.brand_no AND b.deleted = 0
                 INNER JOIN {{ ods_source("mysql_rds_dsrp", "bdc_company", "") }} f
                            ON g.enterprise_no = b.enterprise_no AND g.factory_no = f.company_no AND f.deleted = 0
                 INNER JOIN {{ ods_source("mysql_rds_dsrp", "bdc_goods_category", "") }} c
                            ON g.enterprise_no = b.enterprise_no AND g.category_no = c.category_no AND c.deleted = 0
        WHERE g.deleted = 0
-- 产品过滤条件
    ),

-- 2. 供应商维度数据（预处理所有供应商信息）
    supplier_info AS (
SELECT
    s.enterprise_no,
    s.supplier_no,
    s.supplier_code,
    s.supplier_name
FROM {{ ods_source("mysql_rds_dsrp", "bdc_supplier", "") }} s
WHERE s.deleted = 0
    ),


-- 3. 组织维度数据（预处理所有组织信息）
    organization_info AS (
SELECT
    o.enterprise_no,
    o.org_no,
    o.org_code,
    o.org_name
FROM {{ ods_source("mysql_rds_ddc", "uim_organization", "") }} o
    ),

-- 4. 合同类型维度数据（预处理协议类型信息）
    contract_type_info AS (
SELECT
    ct_type_code,
    ct_type_name
FROM {{ ods_source("mysql_rds_dsrp", "ct_contract_type", "") }}
WHERE   deleted = 0
    ),

-- 5. 采购模式维度数据（预处理采购模式信息）
    purchase_model_info AS (
SELECT
    model_code,
    model_name
FROM {{ ods_source("mysql_rds_dsrp", "ct_purchase_model", "") }}
WHERE deleted = 0
    ),

-- 6. 条款维度数据（预处理条款参数信息）
    common_params AS (
SELECT
    enterprise_no,
    param_code,
    param_name
FROM {{ ods_source("mysql_rds_dsrp", "bdc_common_param", "") }}
WHERE status = 1 AND deleted = 0
    ),


-- 第二层：协议查询（类似Java中的主表查询）
-- 7. 采购协议（先查询满足条件的协议）
    purchase_contracts AS (
SELECT
    'purchase' AS src,
    cp.enterprise_no,
    cp.contract_no,
    cp.contract_name,
    cp.sign_subject_no,
    cp.pur_org_no,
    cp.trans_type_no,
    cp.ct_category_code,
    cp.pur_model_code,
    cp.supplier_code,
    cp.ct_status,
    cp.sign_time,
    cp.plan_effective_time,
    cp.plan_expire_time,
    cp.is_long,
    cp.audit_status,
    cp.create_time,
    cp.modify_time,
    -- 明细信息
    cpi.item_unique_id,
    cpi.sku_code,
    cpi.biz_attr,
    cpi.tax_rate,
    cpi.price,
    cpi.num,
    cpi.amount,
    cpi.cooperation_method_code
FROM {{ ods_source("mysql_rds_dsrp", "ct_purchase", "") }} cp
    INNER JOIN {{ ods_source("mysql_rds_dsrp", "ct_purchase_item", "") }} cpi
ON cp.contract_no = cpi.contract_no AND cp.enterprise_no = cpi.enterprise_no
WHERE cp.deleted = 0 AND cp.audit_status = 1 AND cp.ct_category_code = 'CGJGXY'
  AND cpi.deleted = 0
-- 协议过滤条件（在主查询阶段就过滤）
-- 时间过滤条件
-- 物料过滤条件
-- 价格过滤条件
-- 状态过滤条件
    ),

-- 8. 内部协议（先查询满足条件的协议）
    associate_contracts AS (
SELECT
    'associate' AS src,
    cp.enterprise_no,
    cp.contract_no,
    cp.contract_name,
    cp.sign_subject_no,
    cp.supplier_org_no AS pur_org_no,
    cp.trans_type_no,
    cp.ct_category_code,
    cp.pur_model_code,
    cp.supplier_org_code AS supplier_code,
    cp.ct_status,
    cp.sign_time,
    cp.plan_effective_time,
    cp.plan_expire_time,
    cp.is_long,
    cp.audit_status,
    cp.create_time,
    cp.modify_time,
    -- 明细信息
    cpi.item_unique_id,
    cpi.sku_code,
    cpi.biz_attr,
    cpi.tax_rate,
    cpi.price,
    cpi.num,
    cpi.amount,
    cpi.cooperation_method_code
FROM {{ ods_source("mysql_rds_dsrp", "ct_associate_agreement", "") }} cp
    INNER JOIN {{ ods_source("mysql_rds_dsrp", "ct_associate_item", "") }} cpi
ON cp.contract_no = cpi.contract_no AND cp.enterprise_no = cpi.enterprise_no
WHERE cp.deleted = 0 AND cp.audit_status = 1 AND cp.ct_category_code = 'GLJGXY'
  AND cpi.deleted = 0
-- 协议过滤条件（字段映射调整）
-- 时间过滤条件
-- 物料过滤条件
-- 价格过滤条件
-- 状态过滤条件
    ),

-- 9. 合并所有协议主数据
    all_contracts AS (
SELECT * FROM purchase_contracts
UNION ALL
SELECT * FROM associate_contracts
    ),


-- 第三层：扩展信息查询（类似Java中的关联查询）
-- 10. 条款信息（基于已查询的协议）
    contract_clauses_info AS (
SELECT
    cc.enterprise_no,
    cc.contract_no,
    cc.item_unique_id,
    cc.parameter_no,
    cc.parameter_value,
    cd.param_name,
    'purchase' AS src
FROM {{ ods_source("mysql_rds_dsrp", "ct_purchase_clauses", "") }} cc
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'purchase'
    ) acm ON cc.enterprise_no = acm.enterprise_no AND cc.contract_no = acm.contract_no
    LEFT JOIN common_params cd
    ON cc.enterprise_no = cd.enterprise_no AND cc.parameter_no = cd.param_code
WHERE cc.status = 1 AND cc.deleted = 0

UNION ALL

SELECT
    ac.enterprise_no,
    ac.contract_no,
    ac.item_unique_id,
    ac.parameter_no,
    ac.parameter_value,
    cd.param_name,
    'associate' AS src
FROM {{ ods_source("mysql_rds_dsrp", "ct_associate_clauses", "") }} ac
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'associate'
    ) acm ON ac.enterprise_no = acm.enterprise_no AND ac.contract_no = acm.contract_no
    LEFT JOIN common_params cd
    ON ac.enterprise_no = cd.enterprise_no AND ac.parameter_no = cd.param_code
WHERE ac.status = 1 AND ac.deleted = 0
    ),

-- 11. 适用组织信息（基于已查询的协议）
    applicable_orgs_info AS (
SELECT
    ao.enterprise_no,
    ao.contract_no,
    GROUP_CONCAT(DISTINCT od.org_name) AS applicable_org_names,
    COUNT(DISTINCT ao.pur_org_no) AS org_count
FROM (
    -- 采购协议适用组织
    SELECT
    po.enterprise_no, po.contract_no,
    COALESCE(pco.control_purchase_org_no, po.pur_org_no) AS pur_org_no
    FROM {{ ods_source("mysql_rds_dsrp", "ct_purchase_org", "") }} po
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'purchase'
    ) acm ON po.enterprise_no = acm.enterprise_no AND po.contract_no = acm.contract_no
    LEFT JOIN {{ ods_source("mysql_rds_dsrp", "ctl_purchase_control_org", "") }} pco
    ON po.pur_org_group_id = pco.org_group_id AND pco.deleted = 0
    WHERE po.status = 1 AND po.deleted = 0

    UNION ALL

    -- 内部协议适用组织
    SELECT
    ao.enterprise_no, ao.contract_no,
    COALESCE(pco.control_purchase_org_no, ao.pur_org_no) AS pur_org_no
    FROM {{ ods_source("mysql_rds_dsrp", "ct_associate_org", "") }} ao
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'associate'
    ) acm ON ao.enterprise_no = acm.enterprise_no AND ao.contract_no = acm.contract_no
    LEFT JOIN {{ ods_source("mysql_rds_dsrp", "ctl_purchase_control_org", "") }} pco
    ON ao.pur_org_group_id = pco.org_group_id AND pco.deleted = 0
    WHERE ao.status = 1 AND ao.deleted = 0
    ) ao
    LEFT JOIN organization_info od
ON ao.enterprise_no = od.enterprise_no AND ao.pur_org_no = od.org_no
WHERE 1 = 1
GROUP BY ao.enterprise_no, ao.contract_no
    ),

-- 第四层：最终结果集构建（类似Java中的VO组装）
    dwd_final_price_contract AS (

SELECT
    a.enterprise_no AS enterprise_no,
    a.contract_no AS contract_no,
    a.contract_name AS contract_name,
    a.supplier_code AS supplier_code,
    a.ct_category_code AS ct_category_code,
    -- 时间维度字段 --
    a.plan_effective_time AS plan_effective_time,
    CASE WHEN a.is_long = 1 THEN '9999-12-31' ELSE a.plan_expire_time END AS plan_expire_time,
    a.sign_time AS sign_time,
    a.create_time AS create_time,
    a.modify_time AS modify_time,
    --  协议明细 ------
    a.sku_code AS sku_code,
    case a.biz_attr
    when 1 then '采购'
    when 2 then '联动'
    when 3 then '赠品'
    end AS biz_attr_name,
    a.tax_rate AS tax_rate,
    a.price AS price,
    a.num AS num,
    a.amount AS amount,

    a.sign_subject_no as sign_subject_no, -- doris新增
    a.trans_type_no as trans_type_no, -- doris新增
    a.pur_model_code as pur_model_code, -- doris新增
    a.cooperation_method_code as cooperation_method_code, -- doris新增
    a.ct_status as ct_status, -- doris新增

    -- 关联信息填充 --
    -- 产品信息填充 --
    b.goods_name AS goods_name,
    b.brand_no AS brand_no,
    b.brand_name AS brand_name,
    b.factory_name AS factory_name,
    b.category_no AS category_no,
    b.category_name AS category_name,
    b.common_name AS common_name,
    b.instrument_model AS instrument_model,
    b.spec AS spec,
    b.register_code AS register_code,
    -- 供应商信息填充 --
    c.supplier_no AS supplier_no,
--     c.supplier_code AS supplier_code,
    c.supplier_name AS supplier_name,
    -- 签约组织填充 --
    d.org_code AS sign_org_code,
    d.org_name AS sign_org_name,
    -- 管理组织填充 --

    e.org_no AS pur_org_no, -- doris新增

    e.org_code AS pur_org_code,
    e.org_name AS pur_org_name,
    -- 合同类型填充 --
    f.ct_type_name AS ct_type_name,
    -- 采购模式填充 --
    g.model_name AS model_name,
    -- 填充适用组织 --
    h.applicable_org_names AS applicable_org_names,
    -- 填充条款信息 --
    cci.parameter_no AS parameter_no,
    cci.param_name AS param_name,
    cci.parameter_value AS parameter_value

FROM all_contracts a
-- 关联产品维度（预处理的维度数据）
    INNER JOIN goods_info b
ON a.enterprise_no = b.enterprise_no AND a.sku_code = b.sku_code
-- 关联供应商维度（预处理的维度数据）
    INNER JOIN supplier_info c
    ON a.enterprise_no = c.enterprise_no AND a.supplier_code = c.supplier_code
-- 关联签约组织维度（预处理的维度数据）
    LEFT JOIN organization_info d
    ON a.enterprise_no = d.enterprise_no AND a.sign_subject_no = d.org_no
-- 关联管理组织维度（预处理的维度数据）
    LEFT JOIN organization_info e
    ON a.enterprise_no = e.enterprise_no AND a.pur_org_no = e.org_no
-- 关联合同类型维度（预处理的维度数据）
    LEFT JOIN contract_type_info f
    ON a.trans_type_no = f.ct_type_code
-- 关联采购模式维度（预处理的维度数据）
    LEFT JOIN purchase_model_info g
    ON a.pur_model_code = g.model_code
-- 关联适用组织信息
    LEFT JOIN applicable_orgs_info h
    ON a.enterprise_no = h.enterprise_no AND a.contract_no = h.contract_no
-- 关联条款信息（基于主数据的扩展查询）
    LEFT JOIN contract_clauses_info cci
    ON a.enterprise_no = cci.enterprise_no AND a.contract_no = cci.contract_no
    )

-- 最终查询明细级数据
SELECT
enterprise_no,
    contract_no,
    contract_name,
    cast(supplier_code as varchar(50)) as supplier_code,
    ct_category_code,
    plan_effective_time,
    plan_expire_time,
    sign_time,
    create_time,
    modify_time,
    sku_code,
    biz_attr_name,
    tax_rate,
    price,
    num,
    amount,
    sign_subject_no,
    trans_type_no,
    pur_model_code,
    cooperation_method_code,
    ct_status,
    goods_name,
    brand_no,
    brand_name,
    factory_name,
    category_no,
    category_name,
    common_name,
    instrument_model,
    spec,
    register_code,
    supplier_no,
    supplier_name,
    sign_org_code,
    sign_org_name,
    pur_org_no,
    pur_org_code,
    pur_org_name,
    ct_type_name,
    model_name,
    applicable_org_names,
    parameter_no,
    param_name,
    parameter_value,
now() as update_ts
    FROM dwd_final_price_contract
ORDER BY
    contract_no DESC,
    supplier_name,
    sign_org_name,
    sku_code
```

### 采购订单

*   [ ] 待补充采购订单 sql
    

### 采购入库单

*   [ ] 待补充采购入库单 sql
    

## Excel 模板建表

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/6G3b1AYJrcqLAJjY/6a74e6b2a84b4d01a378b2e0ee96c8260783.png)

表结构在 dbt 项目下统一管理（所有的 doris 表都需要有据可循），把建表语句放在 seed 目录下

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1X3lE5jobeAPmlJb/img/5e9865f9-ea18-45a3-a231-664eb986243d.png)

*   [ ] 模板待定
    

## 接口

| 接口 | 说明 |
| --- | --- |
| querySchemaMeta | 获取数据源（本期只支持 doris 数据源，默认为 doris）的所有 schema 元数据 |
| queryTableMetaInSchema | 获取指定 schema 下的所有 table 元数据 |
| queryColumnMetaInTable | 获取指定 table 下的所有 column 元数据 |

# 数据集

## 挑选表

*   支持 2 种类型：单表和多表 join
    
*   指定驱动表
    
    *   单表时仅填驱动表
        
*   支持多字段作为 on 条件
    

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1X3lE5jobeAPmlJb/img/db4a2d76-05aa-4f60-9fdc-163fbd157fe9.png)

## 挑选字段

*   字段来源于数据集的驱动表、关联表
    
*   表去重
    
*   自动带出信息：
    
    *   显示名称，默认和字段名称一致，可二次修改
        
    *   字段日期格式，格式可二次调整（通过 comment 可能无法获取精确的格式）
        

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1X3lE5jobeAPmlJb/img/cbdd09c3-0e8b-4e96-8d2c-9d41aeb3dfbe.png)

## 数据预览

1.  select：数据集挑选出来的字段（背后隐含了字段所在的表和 schema）
    

*   select schemaX.tableA.columnA
    

1.  from：
    

*   选择数据集中的驱动表
    
    *   from schemaX.tableA
        
*   数据集中关联的模型和模型间的 join 类型
    
    *   from schemaX.tableA inner join schemaX.tableB
        
*   模型 join 时关联的字段和操作符
    
    *   on schemaX.tableA.columnA = schemaX.tableB.columnB
        

1.  limit：默认展示 10 条
    

*   limit 10
    

## 枚举

| 枚举项 | 枚举值 |
| --- | --- |
| 关联关系 | *   innerJoin<br>    <br>*   leftJoin<br>    <br>*   rightJoin |
| 字段操作符 | *   \=<br>    <br>*   ~=<br>    <br>*   \><br>    <br>*   \>=<br>    <br>*   <<br>    <br>*   <= |

## 合法性校验

由于 on 条件手写，存在误写的情况，在进行挑选字段前需要进行校验。

1.  使用 calcite 解析出 on 条件的字段和表名
    
2.  load 涉及到的表的 schema
    
3.  判断字段是否在表中
    

## 接口

| 接口 | 说明 |
| --- | --- |
| saveDataset | 编辑完数据集后，保存数据集和添加的模型字段 |
| getDataset | 根据数据集编号查询数据集详情，包含数据集字段 |
| previewData | 预览数据集记录 |
| validateDataset | 校验数据集配置的字段是否合法 |

# 维度

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/6G3b1AYJrcqLAJjY/ba4f15d007314f679bd835cf940ab6ea0783.png)

## 定义维度表

## ![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1X3lE5jobeAPmlJb/img/15d5e1b2-2c9c-4b56-905b-ae843879d718.png)

## 关联数据集维度字段

> 下图参考 QuickBI

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1X3lE5jobeAPmlJb/img/94dbae34-3e40-4989-b89e-1442a8319ae6.png)

### 接口

| 接口 | 说明 |
| --- | --- |
| saveDimension | 编辑完维度后，保存维度 |
| queryDimensionByDataset | 根据数据集编号查询所有维度列表 |
| getDimensionByNo | 根据维度编号查询维度详情 |

# 指标

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1X3lE5jobeAPmlJb/img/9ed63ddb-cfc1-4e4b-8a06-420523c692d1.png)

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/6G3b1AYJrcqLAJjY/430fd1e6f5924083b5e7d64a939280770783.png)

| 接口 | 说明 |
| --- | --- |
| saveMetric | 编辑完指标后，保存指标 |
| queryMetricByDataset | 根据数据集编号查询所有指标列表 |
| getMetricByNo | 根据指标编号查询指标详情 |