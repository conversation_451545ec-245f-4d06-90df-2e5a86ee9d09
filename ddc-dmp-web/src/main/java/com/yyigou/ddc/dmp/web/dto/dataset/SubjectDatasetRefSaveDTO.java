package com.yyigou.ddc.dmp.web.dto.dataset;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class SubjectDatasetRefSaveDTO implements Serializable {
    /**
     * 主键ID
     */
//    private Long id;

    /**
     * 租户编号
     */
//    private String enterpriseNo;

    /**
     * 主体和数据集关系编号
     */
    private String subjectDatasetRefNo;

    /**
     * 分析主题ID
     */
    private String subjectNo;

    /**
     * 数据集ID
     */
    private String datasetNo;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    private Integer status;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人编号
     */
    private String modifyNo;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 最后操作时间
     */
    private LocalDateTime opTimestamp;
}
