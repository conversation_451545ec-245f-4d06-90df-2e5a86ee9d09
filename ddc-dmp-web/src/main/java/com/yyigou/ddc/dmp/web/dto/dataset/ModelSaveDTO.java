package com.yyigou.ddc.dmp.web.dto.dataset;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class ModelSaveDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
//    private Long id;

    /**
     * 租户编号
     */
//    private String enterpriseNo;

    /**
     * 模型唯一标识
     */
    private String modelNo;

    /**
     * 数据模型名称
     */
    private String modelName;

    /**
     * 数据源类型:sql,table
     */
    private String sourceType;

    /**
     * 逻辑sql
     */
    private String sqlView;

    /**
     * 模型备注或描述
     */
    private String description;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    private Integer status;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

    private List<ModelFieldSaveDTO> modelFieldSaveList;

}
