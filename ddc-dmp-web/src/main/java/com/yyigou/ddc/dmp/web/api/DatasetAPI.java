package com.yyigou.ddc.dmp.web.api;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetModelRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetPreviewDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetValidateDTO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetModelRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetPreviewVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetValidateVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetVO;

public interface DatasetAPI extends ServiceBase {
    /**
     * 保存数据集
     *
     * @param params
     * @return
     */
    CallResult<DatasetVO> save(DatasetSaveDTO params);

    /**
     * 获取数据集
     *
     * @param params
     * @return
     */
    CallResult<DatasetVO> get(DatasetGetDTO params);

    /**
     * 保存数据集和模型关系
     *
     * @param params
     * @return
     */
    CallResult<DatasetModelRefSaveVO> saveDatasetModelRef(DatasetModelRefSaveDTO params);

    /**
     * 预览数据集数据
     *
     * @param params
     * @return
     */
    CallResult<DatasetPreviewVO> previewData(DatasetPreviewDTO params);

    /**
     * 校验数据集配置
     *
     * @param params
     * @return
     */
    CallResult<DatasetValidateVO> validateDataset(DatasetValidateDTO params);
}
