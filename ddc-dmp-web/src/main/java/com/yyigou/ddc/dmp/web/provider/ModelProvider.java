package com.yyigou.ddc.dmp.web.provider;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnBO;
import com.yyigou.ddc.dmp.model.req.dataset.ModelGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.ModelRefGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.ModelRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.ModelSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRefRes;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRes;
import com.yyigou.ddc.dmp.service.dataset.DataModelService;
import com.yyigou.ddc.dmp.service.sqlextractor.SqlExtractorService;
import com.yyigou.ddc.dmp.web.api.ModelAPI;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelRefGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SqlExtractorDTO;
import com.yyigou.ddc.dmp.web.vo.dataset.ColumnVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelRefVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelSaveVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelVO;
import com.yyigou.ddc.dmp.web.vo.dataset.TableVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Interface(name = "模型")
@Component("modelProvider")
public class ModelProvider extends ServiceBaseAbstract implements ModelAPI {
    @Resource
    private SqlExtractorService sqlExtractorService;

    @Resource
    private DataModelService dataModelService;


    /**
     * 获取单个报表模板信息
     */
    @Override
    @Method(aliasName = "ddc.dmp.model.extract", name = "抽取sql元信息", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<List<ColumnVO>> extract(SqlExtractorDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            String sql = params.getSql();
            ValidatorUtil.checkEmptyThrowEx(sql, "sql不能为空");

//        SqlExtractorReq getReq = BeanCopyUtil.copyFields(getDTO.getParams(), SqlExtractorReq.class);
//        getReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            List<ColumnBO> columnBOS = sqlExtractorService.extractTableInfoFromSql(sql);

            List<ColumnVO> columnVOS = columnBOS.stream().map(columnBO -> {
                ColumnVO columnVO = BeanCopyUtil.copyFields(columnBO, ColumnVO.class);
                columnVO.setTableVO(BeanCopyUtil.copyFields(columnBO.getTableBO(), TableVO.class));
                return columnVO;
            }).collect(Collectors.toList());

            return CallResult.success(columnVOS);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存模型和字段
     */
    @Override
    @Method(aliasName = "ddc.dmp.model.save", name = "保存模型", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<ModelSaveVO> save(ModelSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            ModelSaveReq modelSaveReq = BeanCopyUtil.copyFieldsByJson(params, ModelSaveReq.class);
//        modelSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            modelSaveReq.setEnterpriseNo("2000002");
            String modelNo = dataModelService.saveModel(modelSaveReq);

            ModelSaveVO modelSaveVO = new ModelSaveVO();
            modelSaveVO.setModelNo(modelNo);
            return CallResult.success(modelSaveVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查看模型和字段
     */
    @Override
    @Method(aliasName = "ddc.dmp.model.get", name = "查看模型", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<ModelVO> get(ModelGetDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getModelNo(), "模型唯一标识不能为空");

            ModelGetReq detailGetReq = BeanCopyUtil.copyFieldsByJson(params, ModelGetReq.class);
//        detailGetReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            detailGetReq.setEnterpriseNo("2000002");
            ModelRes modelRes = dataModelService.getModel(detailGetReq);

            if (null == modelRes) {
                throw new BusinessException("模型不存在");
            }

            ModelVO modelVO = BeanCopyUtil.copyFields(modelRes, ModelVO.class);
            return CallResult.success(modelVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存模型关系
     */
    @Override
    @Method(aliasName = "ddc.dmp.model.ref.save", name = "保存模型关系", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<ModelRefSaveVO> saveModelRef(ModelRefSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            ModelRefSaveReq modelRefSaveReq = BeanCopyUtil.copyFieldsByJson(params, ModelRefSaveReq.class);
//        modelRefSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            modelRefSaveReq.setEnterpriseNo("2000002");
            String modelRefNo = dataModelService.saveModelRef(modelRefSaveReq);

            ModelRefSaveVO modelRefSaveVO = new ModelRefSaveVO();
            modelRefSaveVO.setModelRefNo(modelRefNo);
            return CallResult.success(modelRefSaveVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查看模型关系
     */
    @Override
    @Method(aliasName = "ddc.dmp.model.ref.get", name = "查看模型关系", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<Map<String, List<ModelRefVO>>> getModelRef(ModelRefGetDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getModelNo(), "模型唯一标识不能为空");

            Map<String, List<ModelRefVO>> result = new HashMap<>();

            ModelRefGetReq modelRefGetReq = BeanCopyUtil.copyFieldsByJson(params, ModelRefGetReq.class);
//        modelRefGetReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            modelRefGetReq.setEnterpriseNo("2000002");
            Map<String, List<ModelRefRes>> modelRef = dataModelService.getModelRef(modelRefGetReq);
            List<ModelRefRes> source = modelRef.get("source");
            if (CollectionUtils.isNotEmpty(source)) {
                List<ModelRefVO> modelRefVOS = BeanCopyUtil.copyFieldsList(source, ModelRefVO.class);
                result.put("source", modelRefVOS);
            }

            List<ModelRefRes> target = modelRef.get("target");
            if (CollectionUtils.isNotEmpty(target)) {
                List<ModelRefVO> modelRefVOS = BeanCopyUtil.copyFieldsList(target, ModelRefVO.class);
                result.put("target", modelRefVOS);
            }

            return CallResult.success(result);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
