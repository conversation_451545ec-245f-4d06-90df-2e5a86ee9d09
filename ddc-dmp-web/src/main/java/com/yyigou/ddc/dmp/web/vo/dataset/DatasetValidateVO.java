package com.yyigou.ddc.dmp.web.vo.dataset;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class DatasetValidateVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据集唯一标识
     */
    private String datasetNo;

    /**
     * 校验是否通过
     */
    private Boolean valid;

    /**
     * 校验错误信息列表
     */
    private List<ValidationError> errors;

    /**
     * 校验成功的字段列表
     */
    private List<ValidField> validFields;

    @Data
    public static class ValidationError implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 错误类型：FIELD_NOT_FOUND, TABLE_NOT_FOUND, INVALID_JOIN_CONDITION
         */
        private String errorType;

        /**
         * 错误消息
         */
        private String message;

        /**
         * 错误相关的表名
         */
        private String tableName;

        /**
         * 错误相关的字段名
         */
        private String fieldName;
    }

    @Data
    public static class ValidField implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 字段数据类型
         */
        private String dataType;

        /**
         * 字段来源表
         */
        private String sourceTable;

        /**
         * 字段来源schema
         */
        private String sourceSchema;
    }
}
