package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetModelRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetPreviewReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetValidateRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetModelRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetPreviewDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetValidateDTO;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetModelRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetPreviewVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetValidateVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 数据集前端控制器
 */
@RestController
@RequestMapping("/dataset")
public class DatasetController {
    @Resource
    private DatasetService datasetService;

    /**
     * 保存数据集
     */
    @PostMapping("/save")
    public DatasetVO save(@RequestBody CommonDTO<DatasetSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), DatasetSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetSaveReq.setEnterpriseNo("2000002");
        String datasetNo = datasetService.saveDataset(datasetSaveReq);

        DatasetVO datasetVO = new DatasetVO();
        datasetVO.setDatasetNo(datasetNo);
        return datasetVO;
    }

    /**
     * 查看数据集
     */
    @GetMapping("/get")
    public DatasetVO get(@RequestBody CommonDTO<DatasetGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);
        ValidatorUtil.checkEmptyThrowEx(getDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetGetReq datasetGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), DatasetGetReq.class);
//        detailGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetGetReq.setEnterpriseNo("2000002");
        DatasetDetailRes datasetDetailRes = datasetService.getDataset(datasetGetReq);

        if (null == datasetDetailRes) {
            throw new BusinessException("数据集不存在");
        }

        DatasetVO datasetVO = BeanCopyUtil.copyFields(datasetDetailRes, DatasetVO.class);
        return datasetVO;
    }

    /**
     * 保存数据集和模型关系
     */
    @PostMapping("/ref/save")
    public DatasetModelRefSaveVO saveModelRef(@RequestBody CommonDTO<DatasetModelRefSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        DatasetModelRefSaveReq datasetModelRefSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), DatasetModelRefSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetModelRefSaveReq.setEnterpriseNo("2000002");
        String datasetModelRefNo = datasetService.saveDatasetModelRef(datasetModelRefSaveReq);

        DatasetModelRefSaveVO datasetModelRefSaveVO = new DatasetModelRefSaveVO();
        datasetModelRefSaveVO.setDatasetModelRefNo(datasetModelRefNo);
        return datasetModelRefSaveVO;
    }

    /**
     * 预览数据集数据
     */
    @PostMapping("/preview")
    public DatasetPreviewVO previewData(@RequestBody CommonDTO<DatasetPreviewDTO> previewDto) {
        CommonParamsUtil.validateCommonDTO(previewDto);
        ValidatorUtil.checkEmptyThrowEx(previewDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetPreviewReq datasetPreviewReq = BeanCopyUtil.copyFieldsByJson(previewDto.getParams(), DatasetPreviewReq.class);
//        datasetPreviewReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetPreviewReq.setEnterpriseNo("2000002");
        DatasetPreviewRes datasetPreviewRes = datasetService.previewData(datasetPreviewReq);

        DatasetPreviewVO datasetPreviewVO = BeanCopyUtil.copyFields(datasetPreviewRes, DatasetPreviewVO.class);
        return datasetPreviewVO;
    }

    /**
     * 校验数据集配置
     */
    @PostMapping("/validate")
    public DatasetValidateVO validateDataset(@RequestBody CommonDTO<DatasetValidateDTO> validateDto) {
        CommonParamsUtil.validateCommonDTO(validateDto);
        ValidatorUtil.checkEmptyThrowEx(validateDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetValidateReq datasetValidateReq = BeanCopyUtil.copyFieldsByJson(validateDto.getParams(), DatasetValidateReq.class);
//        datasetValidateReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetValidateReq.setEnterpriseNo("2000002");
        DatasetValidateRes datasetValidateRes = datasetService.validateDataset(datasetValidateReq);

        DatasetValidateVO datasetValidateVO = BeanCopyUtil.copyFields(datasetValidateRes, DatasetValidateVO.class);
        return datasetValidateVO;
    }
}