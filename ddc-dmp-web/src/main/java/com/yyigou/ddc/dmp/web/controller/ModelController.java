package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnBO;
import com.yyigou.ddc.dmp.model.req.dataset.ModelGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.ModelRefGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.ModelRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.ModelSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRefRes;
import com.yyigou.ddc.dmp.model.res.dataset.ModelRes;
import com.yyigou.ddc.dmp.service.dataset.DataModelService;
import com.yyigou.ddc.dmp.service.sqlextractor.SqlExtractorService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.*;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.dataset.*;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 元数据前端控制器
 */
@RestController
@RequestMapping("/model")
public class ModelController {
    @Resource
    private SqlExtractorService sqlExtractorService;

    @Resource
    private DataModelService dataModelService;

    /**
     * 抽取sql字段信息
     */
    @GetMapping("/extract")
    public List<ColumnVO> extract(@RequestBody CommonDTO<SqlExtractorDTO> getDTO) {
        CommonParamsUtil.validateCommonDTO(getDTO);
        String sql = getDTO.getParams().getSql();
        ValidatorUtil.checkEmptyThrowEx(sql, "sql不能为空");

//        SqlExtractorReq getReq = BeanCopyUtil.copyFields(getDTO.getParams(), SqlExtractorReq.class);
//        getReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        List<ColumnBO> columnBOS = sqlExtractorService.extractTableInfoFromSql(sql);

        List<ColumnVO> columnVOS = columnBOS.stream().map(columnBO -> {
            ColumnVO columnVO = BeanCopyUtil.copyFields(columnBO, ColumnVO.class);
            columnVO.setTableVO(BeanCopyUtil.copyFields(columnBO.getTableBO(), TableVO.class));
            return columnVO;
        }).collect(Collectors.toList());

        return columnVOS;
    }

    /**
     * 保存模型和字段
     */
    @PostMapping("/save")
    public ModelSaveVO save(@RequestBody CommonDTO<ModelSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        ModelSaveReq modelSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), ModelSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        modelSaveReq.setEnterpriseNo("2000002");
        String modelNo = dataModelService.saveModel(modelSaveReq);

        ModelSaveVO modelSaveVO = new ModelSaveVO();
        modelSaveVO.setModelNo(modelNo);
        return modelSaveVO;
    }

    /**
     * 查看模型和字段
     */
    @GetMapping("/get")
    public ModelVO get(@RequestBody CommonDTO<ModelGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);
        ValidatorUtil.checkEmptyThrowEx(getDto.getParams().getModelNo(), "模型唯一标识不能为空");

        ModelGetReq detailGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), ModelGetReq.class);
//        detailGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        detailGetReq.setEnterpriseNo("2000002");
        ModelRes modelRes = dataModelService.getModel(detailGetReq);

        if (null == modelRes) {
            throw new BusinessException("模型不存在");
        }

        ModelVO modelVO = BeanCopyUtil.copyFields(modelRes, ModelVO.class);
        return modelVO;
    }

    /**
     * 保存模型和字段
     */
    @PostMapping("/ref/save")
    public ModelRefSaveVO saveModelRef(@RequestBody CommonDTO<ModelRefSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        ModelRefSaveReq modelRefSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), ModelRefSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        modelRefSaveReq.setEnterpriseNo("2000002");
        String modelRefNo = dataModelService.saveModelRef(modelRefSaveReq);

        ModelRefSaveVO modelRefSaveVO = new ModelRefSaveVO();
        modelRefSaveVO.setModelRefNo(modelRefNo);
        return modelRefSaveVO;
    }

    /**
     * 查看模型关系
     */
    @GetMapping("/ref/get")
    public Map<String, List<ModelRefVO>> getModelRef(@RequestBody CommonDTO<ModelRefGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);
        ValidatorUtil.checkEmptyThrowEx(getDto.getParams().getModelNo(), "模型唯一标识不能为空");

        Map<String, List<ModelRefVO>> result = new HashMap<>();

        ModelRefGetReq modelRefGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), ModelRefGetReq.class);
//        detailGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        modelRefGetReq.setEnterpriseNo("2000002");
        Map<String, List<ModelRefRes>> modelRef = dataModelService.getModelRef(modelRefGetReq);
        List<ModelRefRes> source = modelRef.get("source");
        if (CollectionUtils.isNotEmpty(source)) {
            List<ModelRefVO> modelRefVOS = BeanCopyUtil.copyFieldsList(source, ModelRefVO.class);
            result.put("source", modelRefVOS);
        }

        List<ModelRefRes> target = modelRef.get("target");
        if (CollectionUtils.isNotEmpty(target)) {
            List<ModelRefVO> modelRefVOS = BeanCopyUtil.copyFieldsList(source, ModelRefVO.class);
            result.put("target", modelRefVOS);
        }

        return result;
    }

    //写入model和field
    //查询model（携带field）
    //查询field
    //写入modelref和fieldref
    //查询modelref（携带fieldref）


    //写入dataset
    //查询dataset（携带model关系）
    //写入dataset和model的关系

    //写入subject
    //查询subject（协议dataset关系）
    //写入subject和dataset的关系
}