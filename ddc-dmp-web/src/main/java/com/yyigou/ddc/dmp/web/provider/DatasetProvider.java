package com.yyigou.ddc.dmp.web.provider;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetModelRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetPreviewReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetValidateRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.web.api.DatasetAPI;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetModelRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetPreviewDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.DatasetValidateDTO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetModelRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetPreviewVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetValidateVO;
import com.yyigou.ddc.dmp.web.vo.dataset.DatasetVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Interface(name = "数据集")
@Component("datasetProvider")
public class DatasetProvider extends ServiceBaseAbstract implements DatasetAPI {
    @Resource
    private DatasetService datasetService;

    /**
     * 保存数据集
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.save", name = "保存数据集", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<DatasetVO> save(DatasetSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(params, DatasetSaveReq.class);
//        datasetSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetSaveReq.setEnterpriseNo("2000002");
            String datasetNo = datasetService.saveDataset(datasetSaveReq);

            DatasetVO datasetVO = new DatasetVO();
            datasetVO.setDatasetNo(datasetNo);
            return CallResult.success(datasetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查看数据集
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.get", name = "查看数据集", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetVO> get(DatasetGetDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetGetReq datasetGetReq = BeanCopyUtil.copyFieldsByJson(params, DatasetGetReq.class);
//        datasetGetReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetGetReq.setEnterpriseNo("2000002");
            DatasetDetailRes datasetDetailRes = datasetService.getDataset(datasetGetReq);

            if (null == datasetDetailRes) {
                throw new BusinessException("数据集不存在");
            }

            DatasetVO datasetVO = BeanCopyUtil.copyFields(datasetDetailRes, DatasetVO.class);
            return CallResult.success(datasetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存数据集和模型关系
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.ref.save", name = "保存数据集和模型关系", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<DatasetModelRefSaveVO> saveDatasetModelRef(DatasetModelRefSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            DatasetModelRefSaveReq datasetModelRefSaveReq = BeanCopyUtil.copyFieldsByJson(params, DatasetModelRefSaveReq.class);
//        datasetModelRefSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetModelRefSaveReq.setEnterpriseNo("2000002");
            String datasetModelRefNo = datasetService.saveDatasetModelRef(datasetModelRefSaveReq);

            DatasetModelRefSaveVO datasetModelRefSaveVO = new DatasetModelRefSaveVO();
            datasetModelRefSaveVO.setDatasetModelRefNo(datasetModelRefNo);
            return CallResult.success(datasetModelRefSaveVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 预览数据集数据
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.preview", name = "预览数据集数据", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetPreviewVO> previewData(DatasetPreviewDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetPreviewReq datasetPreviewReq = BeanCopyUtil.copyFieldsByJson(params, DatasetPreviewReq.class);
//        datasetPreviewReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetPreviewReq.setEnterpriseNo("2000002");
            DatasetPreviewRes datasetPreviewRes = datasetService.previewData(datasetPreviewReq);

            DatasetPreviewVO datasetPreviewVO = BeanCopyUtil.copyFields(datasetPreviewRes, DatasetPreviewVO.class);
            return CallResult.success(datasetPreviewVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 校验数据集配置
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.validate", name = "校验数据集配置", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetValidateVO> validateDataset(DatasetValidateDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetValidateReq datasetValidateReq = BeanCopyUtil.copyFieldsByJson(params, DatasetValidateReq.class);
//        datasetValidateReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetValidateReq.setEnterpriseNo("2000002");
            DatasetValidateRes datasetValidateRes = datasetService.validateDataset(datasetValidateReq);

            DatasetValidateVO datasetValidateVO = BeanCopyUtil.copyFields(datasetValidateRes, DatasetValidateVO.class);
            return CallResult.success(datasetValidateVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
