package com.yyigou.ddc.dmp.web.api;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelRefGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.ModelSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SqlExtractorDTO;
import com.yyigou.ddc.dmp.web.vo.dataset.ColumnVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelRefVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelVO;
import com.yyigou.ddc.dmp.web.vo.dataset.ModelSaveVO;

import java.util.List;
import java.util.Map;

public interface ModelAPI extends ServiceBase {
    /**
     * 抽取sql的字段等元信息
     *
     * @param params
     * @return
     */
    CallResult<List<ColumnVO>> extract(SqlExtractorDTO params);

    /**
     * 保存模型
     *
     * @param params
     * @return
     */
    CallResult<ModelSaveVO> save(ModelSaveDTO params);

    /**
     * 获取模型
     *
     * @param params
     * @return
     */
    CallResult<ModelVO> get(ModelGetDTO params);

    /**
     * 保存模型关系
     *
     * @param params
     * @return
     */
    CallResult<ModelRefSaveVO> saveModelRef(ModelRefSaveDTO params);

    /**
     * 查看模型关系
     *
     * @param params
     * @return
     */
    CallResult<Map<String, List<ModelRefVO>>> getModelRef(ModelRefGetDTO params);
}
