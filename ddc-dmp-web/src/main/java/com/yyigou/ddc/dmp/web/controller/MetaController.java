package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.model.res.meta.ColumnsRes;
import com.yyigou.ddc.dmp.model.res.meta.SchemataRes;
import com.yyigou.ddc.dmp.model.res.meta.TablesRes;
import com.yyigou.ddc.dmp.service.meta.MetaService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SqlExtractorDTO;
import com.yyigou.ddc.dmp.web.dto.meta.ColumnsQueryDTO;
import com.yyigou.ddc.dmp.web.dto.meta.TablesQueryDTO;
import com.yyigou.ddc.dmp.web.vo.meta.ColumnsVO;
import com.yyigou.ddc.dmp.web.vo.meta.SchemataVO;
import com.yyigou.ddc.dmp.web.vo.meta.TablesVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据源元信息
 */
@RestController
@RequestMapping("/meta")
public class MetaController {
    @Resource
    private MetaService metaService;

    @GetMapping("/querySchemas")
    public List<SchemataVO> querySchemas() {
        List<SchemataRes> schemataRes = metaService.loadSchemata();

        return BeanCopyUtil.copyFieldsList(schemataRes, SchemataVO.class);
    }

    @GetMapping("/queryTables")
    public List<TablesVO> queryTables(@RequestBody CommonDTO<TablesQueryDTO> tablesQueryDTO) {
        List<TablesRes> tablesRes = metaService.loadTables(tablesQueryDTO.getParams().getSchemaName());

        return BeanCopyUtil.copyFieldsList(tablesRes, TablesVO.class);
    }


    @GetMapping("/queryColumns")
    public List<ColumnsVO> queryColumns(@RequestBody CommonDTO<ColumnsQueryDTO> columnsQueryDTO) {
        List<ColumnsRes> columnsRes = metaService.loadColumns(columnsQueryDTO.getParams().getSchemaName(), columnsQueryDTO.getParams().getTableName());

        return BeanCopyUtil.copyFieldsList(columnsRes, ColumnsVO.class);
    }

}